import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { DataService } from '../services/data.service';
import { AlertController, IonSelect, MenuController, ModalController, NavController, Platform } from '@ionic/angular';
import { HelpService } from '../services/help.service';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AlertService } from '../services/alert.service';
import { FileOpener } from '@awesome-cordova-plugins/file-opener/ngx';
import { HTTP } from '@awesome-cordova-plugins/http/ngx';
import { Zip } from '@awesome-cordova-plugins/zip/ngx';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { DocumentViewer, DocumentViewerOptions } from '@awesome-cordova-plugins/document-viewer/ngx';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { GenericListPage } from '../generic-list/generic-list.page';
import { Network } from '@awesome-cordova-plugins/network/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faDownload, faEnvelope, faSortDown, faTasks, faTh } from '@fortawesome/free-solid-svg-icons';
import { PlatformService } from '../services/platform.service';
declare var FileOpener2  : any;
@Component({
  selector: 'app-resource',
  templateUrl: './resource.page.html',
  styleUrls: ['./resource.page.scss'],
})
export class ResourcePage implements OnInit {
  selection: any;
  hideList = true;
  customizationAlert: any;
  platformId: string = this.platformService.getPlatformId();

  @ViewChild('countryList') countrySelectRef: IonSelect;
  isUserEnabledForInsightAI: boolean = false;

  constructor(private router: Router,
    public platformService: PlatformService,
    private inAppBrowser: InAppBrowser,
    public dataService: DataService,
    private menu: MenuController,
    public navCtrl: NavController,
    public alertController: AlertController,
    public helpService: HelpService,
    public plt: Platform,
    public document: DocumentViewer,
    public file: File,
    public unviredSdk: UnviredCordovaSDK,
    public network: Network,
    public modalController: ModalController,
    public alertService: AlertService,
    public fileOpener: FileOpener,
    public device: Device,
    private nativeHTTP: HTTP,
    private zip: Zip,
    public faIconLibrary: FaIconLibrary) { 
      this.faIconLibrary.addIcons(faDownload , faSortDown , faBars , faTasks , faTh, faEnvelope )
     }
  ngOnInit() {
    this.checkUserEnabledForAI();
    if(this.device.platform == "Android") {
      this.checkAndDownloadResources()
    }
  }

  async checkUserEnabledForAI() {
    this.isUserEnabledForInsightAI = await this.dataService.userEnabledForInsightAI();
  }

  ionViewWillEnter() {
    this.selection = null
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  ionViewDidLeave() {
    this.helpService.exitHelpMode();
  }
  back() {
    this.router.navigate(['home']);
  }

  openAbrasion() {
    this.router.navigate(['resource-abrasion'])
  }

  openInspections() {
    this.router.navigate(['resource-inspections'])
  }

  openPdfExternal() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/Abrasion-External.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', "Abrasion-External.pdf").then(() => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/Abrasion-External.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'Abrasion-External.pdf', this.file.dataDirectory, `Abrasion-External.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/Abrasion-External.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openPdfInternal() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/Abrasion-Internal.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `/Abrasion-Internal.pdf`).then(() => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/Abrasion-Internal.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'Abrasion-Internal.pdf', this.file.dataDirectory, `Abrasion-Internal.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/Abrasion-Internal.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openPdfTenex() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/TENEX_Inspection&Retirement_Guide.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `/Abrasion-Internal.pdf`).then(() => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/Abrasion-Internal.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'TENEX_Inspection&Retirement_Guide.pdf', this.file.dataDirectory, `TENEX_Inspection&Retirement_Guide.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/TENEX_Inspection&Retirement_Guide.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }

  }

  openPdfSinglebraid() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/App_SB_Inspection_Checklist_2017.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `/App_SB_Inspection_Checklist_2017.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/App_SB_Inspection_Checklist_2017.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'App_SB_Inspection_Checklist_2017.pdf', this.file.dataDirectory, `App_SB_Inspection_Checklist_2017.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/App_SB_Inspection_Checklist_2017.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openPdfDoublebraid() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/App_DB_Inspection_Checklist_2017.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `/App_DB_Inspection_Checklist_2017.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/App_DB_Inspection_Checklist_2017.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'App_DB_Inspection_Checklist_2017.pdf', this.file.dataDirectory, `App_DB_Inspection_Checklist_2017.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/App_DB_Inspection_Checklist_2017.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openPdfRetirement() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/hmpe-ropes_design-vs-performance_mar2012_web.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `/hmpe-ropes_design-vs-performance_mar2012_web.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/hmpe-ropes_design-vs-performance_mar2012_web.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'hmpe-ropes_design-vs-performance_mar2012_web.pdf', this.file.dataDirectory, `hmpe-ropes_design-vs-performance_mar2012_web.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/hmpe-ropes_design-vs-performance_mar2012_web.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openPdfLocalizedDamage() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/localized_damage_assessment_c2_12strands_web.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `/localized_damage_assessment_c2_12strands_web.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/localized_damage_assessment_c2_12strands_web.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'localized_damage_assessment_c2_12strands_web.pdf', this.file.dataDirectory, `localized_damage_assessment_c2_12strands_web.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/localized_damage_assessment_c2_12strands_web.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openPdfRopeMeasurement() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/rope-measurement.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `/rope-measurement.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/rope-measurement.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'rope-measurement.pdf', this.file.dataDirectory, `rope-measurement.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/rope-measurement.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openPdfEffectOfTwist() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/effect-of-twist-on-braided-rope_mar2012_web.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `/effect-of-twist-on-braided-rope_mar2012_web.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/effect-of-twist-on-braided-rope_mar2012_web.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'effect-of-twist-on-braided-rope_mar2012_web.pdf', this.file.dataDirectory, `effect-of-twist-on-braided-rope_mar2012_web.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `/effect-of-twist-on-braided-rope_mar2012_web.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }

  pageSelectedAbrationGuide() {
    if (this.selection == "External") {
      this.openPdfExternal()
    } else if (this.selection == "Internal") {
      this.openPdfInternal();
    } else if (this.selection == "Tenex") {
      this.openPdfTenex();
    } else if (this.selection == "Abrasion") {
      this.selection = null
      this.openAbrasion()
    }
    setTimeout(() => {
      this.selection = null
    }, 1000);
  }

  pageSelectedInspectionGuide() {
    switch (this.selection) {
      case 'SingleBraid':
        this.openPdfSinglebraid()
        break;
      case 'DoubleBraid':
        this.openPdfDoublebraid();
        break;
      case 'Inspection':
        this.openInspections();
        this.selection = null;
        break;
      case 'Retirement':
        this.openPdfRetirement();
        break;
      case 'LocalizedDamage':
        this.openPdfLocalizedDamage();
        break;
      case 'RopeMeasurement':
        this.openPdfRopeMeasurement();
        break;
      case 'EffectOfTwist':
        this.openPdfEffectOfTwist();
        break;
    }

    setTimeout(() => {
      this.selection = null
    }, 1000);
  }

  pageSelectedUserGuides() {
    switch (this.selection) {
      case 'UserGuide':
        this.openUserGuide();
        break;
      case 'InsightAIQuickInspectGuide':
        this.openInsightAIQuickInspectGuide();
        break;
      case 'InsightAIDetailedInspectionGuide':
        this.openInsightAIDetailedinspGuide();
        this.selection = null;
        break;
      case 'InsightAIRoutineInspectionGuide':
        this.openInsightAIRoutineInspection();
        break;
    }

    setTimeout(() => {
      this.selection = null
    }, 1000);
  }

  openInsightAIRoutineInspection() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + `/ICARIA_Insight_Routine_Inspection_User_Guide_Feb24_WEB.pdf`, 'application/pdf')
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'ICARIA_Insight_Routine_Inspection_User_Guide_Feb24_WEB.pdf', this.file.dataDirectory, `ICARIA_Insight_Routine_Inspection_User_Guide_Feb24_WEB.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `ICARIA_Insight_Routine_Inspection_User_Guide_Feb24_WEB.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openInsightAIDetailedinspGuide() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + `/ICARIA_Insight_Detailed_Inspection_User_Guide_Feb24_WEB.pdf`, 'application/pdf')
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'ICARIA_Insight_Detailed_Inspection_User_Guide_Feb24_WEB.pdf', this.file.dataDirectory, `ICARIA_Insight_Detailed_Inspection_User_Guide_Feb24_WEB.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `ICARIA_Insight_Detailed_Inspection_User_Guide_Feb24_WEB.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  openInsightAIQuickInspectGuide() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + `/ICARIA_Insight_Quick_Inspection_User_Guide_Feb24_WEB.pdf`, 'application/pdf')
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'ICARIA_Insight_Quick_Inspection_User_Guide_Feb24_WEB.pdf', this.file.dataDirectory, `ICARIA_Insight_Quick_Inspection_User_Guide_Feb24_WEB.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `ICARIA_Insight_Quick_Inspection_User_Guide_Feb24_WEB.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  pageSelectedTechnicalGuide() {
    var browser;
    let url=''
    switch (this.selection) {
      case 'AmSteel®-Blue Frequently   Asked Questions':
        url = 'https://samsonrope.com/docs/default-source/technical-bulletins/tb_amsteel-blue_faq.pdf?sfvrsn=726fa50a_8'
        break;
      case 'Coefficient of Friction (CoF)':
        url = browser = 'https://samsonrope.com/docs/default-source/technical-bulletins/tb_coeffecient_of_friction.pdf?sfvrsn=408b2716_2'
        break;
      case 'DPX™ - Innovative Fiber Technology':
        url = 'https://samsonrope.com/docs/default-source/technical-bulletins/tb_dpx_innovative-fiber-tech_sept2012_web.pdf?sfvrsn=3b58d589_2';
        break;
      case 'Elastic Stiffness':
        url = "https://samsonrope.com/docs/default-source/technical-bulletins/technical-bulletin-elastic-stiffness.pdf?sfvrsn=121b46c5_8"
        break;
      case 'HMPE Ropes and Chocks - Closed vs. Roller':
        url = "https://samsonrope.com/docs/default-source/technical-bulletins/tb_hmpe-ropes-and-chocks_closed-vs-roller_mar2007_web.pdf?sfvrsn=6693131b_2"
        break;
      case 'HMPE Rope: Design vs. Performance':
        url = "https://samsonrope.com/docs/default-source/technical-bulletins/tb_hmpe-ropes_design-vs-performance_mar2012_web.pdf?sfvrsn=6c5a59d_2"
        break;
      case 'Proper Handling techniques for Samson 8-Strand High Performance Ropes on   H-Bitts':
        url = "https://samsonrope.com/docs/default-source/technical-bulletins/tb_handling-techniques-for-8strand-on-hbitts_jun2013_web.pdf?sfvrsn=dcf037d1_2";
        break;
      case 'Rope Measurement':
        url = "https://samsonrope.com/docs/default-source/technical-bulletins/tb_rope-measurement.pdf?sfvrsn=e1cbfb20_8";
        break;
      case 'Surface Preparation for Synthetic Ropes':
        url = "https://www.samsonrope.com/docs/default-source/technical-bulletins/tb_surface-prep-for-synthetic-ropes_jun2005_web.pdf?sfvrsn=9169d8d7_10"
        break;
      case 'Type Approvals and Product Certifications':
        url = "https://www.samsonrope.com/docs/default-source/technical-bulletins/type_approvals_and_product_certifications.pdf?sfvrsn=b0e94e98_4"
        break;
      case 'Understanding Creep':
        url = "https://www.samsonrope.com/docs/default-source/technical-bulletins/tb_understanding-creep_mar2012_web.pdf?sfvrsn=1e71fc4e_2"
        break;
      case 'How Cold Can You Go?':
        url = "https://www.samsonrope.com/docs/default-source/technical-bulletins/tb_how-cold-can-you-go_jun2009_web.pdf?sfvrsn=71695d0a_2"
        break;
      case 'Mooring in High-Temperature Climates':
        url = "https://www.samsonrope.com/docs/default-source/technical-bulletins/tb_tanker-mooring-in-hot-climates_sept2012_web.pdf?sfvrsn=b3bc442c_2"
        break;
      case 'Multi-Color Mooring Line Rope End Identification':
        url = "https://www.samsonrope.com/docs/default-source/technical-bulletins/mooring_rope_end_identification?sfvrsn=c2f72042_2"
        break;
      case 'Tug Boat Messenger Line Attachment':
        url = "https://www.samsonrope.com/docs/default-source/technical-bulletins/tb_tug-messenger-line_apr2013_web.pdf?sfvrsn=f0eceef6_2"
        break;
      case 'VULCAN: A Synthetic Emergency Tow-Off Pendant':
        url = "https://www.samsonrope.com/docs/default-source/technical-bulletins/tb_vulcan-etops_sept2010_web.pdf?sfvrsn=2f503a09_2"
        break;
      case 'Rope Care':
        url = "https://www.samsonrope.com/resources/rope-care"
        break;
      case 'MP-1 Performance Results':
        url = "https://www.samsonrope.com/docs/default-source/technical-bulletins/mp-1_performance_results.pdf?sfvrsn=9235a28c_8"
        break;
      case 'Rope Users Manual':
        url = "https://www.samsonrope.com/resources/commercial-marine/rope-manual"
        break;
    }

    if (this.platformId === 'electron') {
      browser = this.inAppBrowser.create(url, '_system', 'location=no,usewkwebview=true');
      browser.show();
    } else if(this.device.platform === 'Android') {
      browser = this.inAppBrowser.create(url, "_system");
    } else {
      browser = this.inAppBrowser.create(url, "_blank");
    }

    if (browser != null) {
      browser.show();
    }
  }

  async presentModal() {
    let type = this.network.type;
    this.unviredSdk.logError("Resources", "PresentModal", "NETWORK_TYPE    ======  " + type)
    // if(type == "unknown" || type == "none" || type == undefined) {
    //   this.alertService.showAlert("", "To access there resources please connect to internet.")
    //   return
    // }
    var isConnectedToNetwork = navigator.onLine
    if (isConnectedToNetwork == false && this.dataService.getNetworkStatus() == false) {
      this.alertService.showAlert("", "To access there resources please connect to internet.")
      return
    }


    var tempList = [
      { "NAME": "AmSteel®-Blue Frequently   Asked Questions", "URL": "", "TYPE": "PDF" },
      { "NAME": "Coefficient of Friction (CoF)", "URL": "", "TYPE": "PDF" },
      { "NAME": "DPX™ - Innovative Fiber Technology", "URL": "", "TYPE": "PDF" },
      { "NAME": "Elastic Stiffness", "URL": "", "TYPE": "PDF" },
      { "NAME": "HMPE Ropes and Chocks - Closed vs. Roller", "URL": "", "TYPE": "PDF" },
      // { "NAME": "HMPE Post-Production Process", "URL": "", "TYPE": "PDF" },
      { "NAME": "HMPE Rope: Design vs. Performance", "URL": "", "TYPE": "PDF" },
      { "NAME": "Proper Handling techniques for Samson 8-Strand High Performance Ropes on   H-Bitts", "URL": "", "TYPE": "PDF" },
      { "NAME": "Rope Measurement", "URL": "", "TYPE": "PDF" },
      { "NAME": "Surface Preparation for Synthetic Ropes", "URL": "", "TYPE": "PDF" },
      { "NAME": "Type Approvals and Product Certifications", "URL": "", "TYPE": "PDF" },
      { "NAME": "Understanding Creep", "URL": "", "TYPE": "PDF" },
      { "NAME": "How Cold Can You Go?", "URL": "", "TYPE": "PDF" },
      { "NAME": "Mooring in High-Temperature Climates", "URL": "", "TYPE": "PDF" },
      { "NAME": "Multi-Color Mooring Line Rope End Identification", "URL": "", "TYPE": "PDF" },
      { "NAME": "Tug Boat Messenger Line Attachment", "URL": "", "TYPE": "PDF" },
      { "NAME": "VULCAN: A Synthetic Emergency Tow-Off Pendant", "URL": "", "TYPE": "PDF" },
      { "NAME": "Rope Care", "URL": "", "TYPE": "PDF" },
      { "NAME": "MP-1 Performance Results", "URL": "", "TYPE": "PDF" },
      { "NAME": "Rope Users Manual", "URL": "", "TYPE": "PDF" }
    ]


    const modal = await this.modalController.create({
      component: GenericListPage,
      componentProps: { value: tempList, title: 'Technical Bulletins', page: 'RESOURCE' }
    });
    await modal.present();


    modal.onDidDismiss().then(async (data) => {
      if (data.data.data != '') {
        this.selection = data.data.data.NAME
        this.pageSelectedTechnicalGuide();
      }
    });
  }

  displayCountry() {
    this.countrySelectRef.open();
  }

  openSpliceInstructions() {
    this.router.navigate(['resources-splice-instruction']);
  }

  async showErrorMessage() {
    const alert = await this.alertController.create({
      header: 'Alert',
      message: 'Resource file not downloaded.',
      buttons: [
        {
          text: 'Ok',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {
          }
        }
      ],
      backdropDismiss: false
    });
    await alert.present();
  }

  async showResourceDownloadLoading() {
    this.customizationAlert = await this.alertController.create({
      header: 'Alert',
      message: '<div><div class="imageStyle"><img src="./assets/img/blue-hourglass.gif"></div>' + 'Please wait while we configure resources. Do not exit this screen.</div>',
      backdropDismiss: false,
      cssClass: 'waitImage',
    });
    await this.customizationAlert.present();
    console.log("Download alert presented")
    this.unviredSdk.logDebug("home", "ionViewWillEnter", " Download alert presented ")
    await this.customizationAlert.onDidDismiss().then((data) => {          
    })
  }

  checkAndDownloadResources() {
    this.file.checkDir(this.file.dataDirectory, "pdf").then(() => {
      console.log("Already Downloaded")
      }).catch((err) => {
        this.showResourceDownloadAlert(true)
        console.log("Download resources")
      });

  }

  async showResourceDownloadAlert(closePage) {
    const alert = await this.alertController.create({
      header: 'Alert',
      message: 'Do you want download resource files?',
      buttons: [
        {
          text: 'Later',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {
            if(closePage == true) {
              this.router.navigateByUrl('/home')
            }
          }
        }, {
          text: 'Yes',
          handler: async () => {
            this.downloadResources();
            // this.route.navigate(['user-preferences']);
          }
        }
      ],
      backdropDismiss: false
    });
    await alert.present();
  }

  async downloadResources() {
    this.showResourceDownloadLoading();
    var url = "https://sandbox.unvired.io/samson/resources.zip"
    var tempUser = await this.unviredSdk.userSettings();
    var tempzSettings = tempUser.data["SERVER_URL"]
    if(tempzSettings.indexOf('/UMP/') > 0) {
      url = tempzSettings.replace('/UMP/','/samson/resources.zip')
    } else {
      url = tempzSettings.replace('/UMP','/samson/resources.zip')
    }
    this.unviredSdk.logInfo("Resources", "downloadResources", 'Download URL' + url);
    this.unviredSdk.logInfo("Resources", "downloadResources", 'Target path' + this.file.dataDirectory);
    const filePath = this.file.dataDirectory + 'dummy.zip';
    this.nativeHTTP.downloadFile(url, {}, {}, filePath).then((entry) => {      
      this.unviredSdk.logInfo("home", "downloadResources", 'download complete: ' + entry.toURL());
      this.file.checkFile(this.file.dataDirectory, "dummy.zip")
        .then(() => {
          this.zip.unzip(this.file.dataDirectory + 'dummy.zip', this.file.dataDirectory, (progress) => this.unviredSdk.logInfo("Resource", "downloadResources", 'Unzipping, ' + Math.round((progress.loaded / progress.total) * 100) + '%'))
          .then((result) => {
            if(result === 0)  {
              this.unviredSdk.logInfo("Resource", "downloadResources",'SUCCESS');
              this.file.checkDir(this.file.dataDirectory, "pdf")
              .then(() => {
              })
              .catch((err) => { 
                this.showAlert("Error :" + JSON.stringify(err))
              });
            }
            if(result === -1) console.log('FAILED');
          });  
          this.customizationAlert.dismiss();
        })
        .catch((err) => { 
          this.customizationAlert.dismiss();
          this.showAlert("Error :" + JSON.stringify(err))
        });
    }, (error) => {
      console.log('download error: ');
      this.customizationAlert.dismiss();
      this.showAlert("Error :" + JSON.stringify(error))
      // handle error
    });
  
  }

  openUserGuide() {
    if(this.device.platform == "Android") {
      this.fileOpener.open(this.file.dataDirectory + `/App_User_Quick_Start_Guide_Apr2021_LINKED.pdf`, 'application/pdf')
      // this.file.checkFile(this.file.dataDirectory + '/pdf', `/effect-of-twist-on-braided-rope_mar2012_web.pdf`).then(async () => {
      //   this.fileOpener.open(this.file.dataDirectory + '/pdf' + `/effect-of-twist-on-braided-rope_mar2012_web.pdf`, 'application/pdf')
      //   }).catch((err) => {
      //     this.showErrorMessage();
      //   });
      
    } else {
      let filePath = this.file.applicationDirectory + 'www/assets';
      let fakeName = Date.now();
      this.file.copyFile(filePath, 'App_User_Quick_Start_Guide_Apr2021_LINKED.pdf', this.file.dataDirectory, `App_User_Quick_Start_Guide_Apr2021_LINKED.pdf`).then(result => {
        this.fileOpener.open(result.nativeURL, 'application/pdf')
      }, error => {
        if (error.code == 12) {
          this.fileOpener.open(this.file.dataDirectory + `App_User_Quick_Start_Guide_Apr2021_LINKED.pdf`, 'application/pdf')
        }
        console.log(JSON.stringify(error))
      });
    }
  }

  async showAlert(message) {
    const alert = await this.alertController.create({
      message: message,
      buttons: ['OK']
    });
    await alert.present();
  }
}
