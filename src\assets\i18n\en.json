{"Delete Image": "Delete Image", "Click to start new Inspection": "Click to start new Inspection", "Start": "Start", "Fax:": "Fax:", "Dashboard": "Dashboard", "Hi Adrianne!": "Hi", "Recent Inspections": "Recent Inspections", "Acme LLC": "Acme LLC", "Acme Corp": "Acme Corp", "Acme Inc": "Acme Inc", "Exit Help": "Exit Help", "Enter the measurement ending point value": "Enter the measurement ending point value", "Enter the measurement ending point of external abrasion": "Enter the measurement ending point of external abrasion", "Describe the ending value in meters": "Describe the ending value", "Select option to see old External Measurements": "Select option to see old External Measurements", "Customer": "Customer", "Date": "Date", "Select Date": "Select Date", "Inspected By": "Inspected By", "Adrianne Czebator": "User", "Save": "Save", "Current": "Current", "Historical": "Historical", "Inspections": "Inspections", "PLocation Installed": "Location Installed", "Planned Inspection": "Planned Inspection", "Ad-hoc Inspection": "Ad-hoc Inspection", "Cert #: 123-ABC-5436-D": "Cert #: 123-ABC-5436-D", "Samson": "<PERSON>", "Inspected": "Inspected", "Location": "Location", "Enter the measurement ending point of internal abrasion": "Enter the measurement ending point of internal abrasion", "Select option to see old Internal Measurements": "Select option to see old Internal Measurements", "Axial Compression": "Axial Compression", "Enter the measurement starting point of axial compression": "Enter the measurement starting point of axial compression", "Enter the measurement ending point of  axial compression": "Enter the measurement ending point of  axial compression", "Enter start point of axial compression": "Enter start point of axial compression", "Describe the measurement point": "Describe the measurement point", "Enter axial compression range": "Enter axial compression range", "Enter the axial compression range' | translate}}<span> ( {{'#0-50": "Enter the axial compression range' | translate}}<span> ( {{'#0-50", "Describe the range value in yarns": "Describe the range value in yarns", "Tap to take a photo of axial compression": "Tap to take a photo of axial compression", "Click to save axial compression": "Click to save axial compression", "Tap to save axial compression measurement": "Tap to save axial compression measurement", "Select option to see other Axial Measurements": "Select option to see other Axial Measurements", "Enter start point where Deformation occur": "Enter start point where Deformation occur", "Describe measurement point": "Describe measurement point", "Tap to take a photo of  basket deformation": "Tap to take a photo of Basket Deformation", "Select option to see other Basket Deformations": "Select option to see other Basket Deformations", "Describe measurement point where caging occur": "Describe measurement point where caging occur", "Select option to see other Bird Cagings": "Select option to see other Bird Caging", "Describe where compression occur": "Describe where compression occur", "Select option to see other Compressions": "Select option to see other Compressions", "Enter the start point of the contamination": "Enter the start point of the contamination", "Click to take photo of the Contamination": "Click to take photo of the Contamination", "Click to save the Contamination measurement": "Click to save the Contamination measurement", "Select option to see other Contaminations": "Select option to see other Contaminations", "Describe which place break occurs": "Describe which place break occurs", "Enter cuts range": "Enter cuts range", "Enter the cut range' | translate}}<span> ( {{'0-50": "Enter the cut range' | translate}}<span> ( {{'0-50", "Describe the value where cuts occur": "Describe the value where cuts occur", "Take a photo of cut measurement": "Take a photo of cut measurement", "Click to save the Cuts Measurement": "Click to save the Cuts Measurement", "Select option to see old Cut Measurements": "Select option to see old Cut Measurements", "Describe the value where discoloration occur": "Describe the value where discoloration occur", "Select option to see other discoloration measurements": "Select option to see other discoloration measurements", "Describe measurement point where flattening occurs": "Describe measurement point where flattening occurs", "Select option to see other Flattenings": "Select option to see other Flattening", "General": "General", "Select Yes if it is General Compression": "Select Yes if it is General Compression", "Select No if it is not General Compression": "Select No if it is not General Compression", "Click to take photo of General Compression": "<PERSON>lick to take photo of General Compression", "Click to reset the General Compression": "<PERSON>lick to reset the General Compression", "Reset": "Reset", "Select option to see other General Compressions": "Select option to see other General Compressions", "Describe the value where Inconsistent diameter occur": "Describe the value where Inconsistent diameter occur", "Inconsistent Diameter type": "Inconsistent Diameter type", "Select option to see old  Inconsistent Diameter Measurements": "Select option to see old  Inconsistent Diameter Measurements", "Describe measurement point where kinking occurs": "Describe measurement point where kinking occurs", "Describe the value where kinking occur": "Describe the value where kinking occur", "Enter the value of kinking range": "Enter the value of kinking range", "Enter the rope kinking range' | translate}}<span> ( {{'0-50 yarns": "Enter the rope kinking range' | translate}}<span> ( {{'0-50 yarns", "Select option to see other Kinkings": "Select option to see other Kinking", "Describe the value where melting occur": "Describe the value where melting occur", "Enter the rope melting range": "Enter the rope melting range", "Enter the rope melting range' | translate}}<span> ( {{'0-50 yarns": "Enter the rope melting range' | translate}}<span> ( {{'0-50 yarns", "Select the rope melting type :": "Type of Damage", "Take a photo of melting": "Take a photo of melting", "Click to save the Melting Measurement": "Click to save the Melting Measurement", "Select option to see old Melting Measurements": "Select option to see old Melting Measurements", "Describe the value where parting occur": "Describe the value where parting occur", "Select option to see other parting measurements": "Select option to see other parting measurements", "Describe measurement point where protusion occur": "Describe measurement point where protrusion occur", "Select option to see other Protusions": "Select option to see other Protrusion", "Describe the value where pulled occur": "Describe the value where pulled occur", "Select the type of damage :": "Type of Damage", "Select option to see old pulled measurements": "Select option to see old pulled measurements", "Radial Compression": "Radial Compression", "Enter the measurement starting point of  radial compression": "Enter the measurement starting point of  radial compression", "Enter the length of radial compression": "Enter the length of radial compression", "Enter value where raial compression occur": "Enter value where radial compression occur", "Enter radial compression range": "Enter radial compression range", "Enter the radial compression range' | translate}}<span> ( {{'0-50": "Enter the radial compression range' | translate}}<span> ( {{'0-50", "Tap to take a photo of radial compression": "Tap to take a photo of radial compression", "Click to save the  Radial Measurement": "Click to save the  Radial Measurement", "Tap to save radial compression measurement": "Tap to save radial compression measurement", "Select option to see other  radial measurements": "Select option to see other  radial measurements", "Describe measurement point where seizing occur": "Describe measurement point where seizing occur", "Select option to see other Seizings": "Select option to see other Seizings", "Enter the starting value of rope": "Enter the starting value of rope", "Describe starting value": "Describe starting value", "Enter the ending value of rope": "Enter the ending value of rope", "Describe ending value": "Describe ending value", "TPF": "TPF", "Select option to see other Twists": "Select option to see other Twists", "Describe the meaurement point where waviness occurs": "Describe the measurement point where waviness occurs", "Select option to see other Wavinesss": "Select option to see other Waviness", "Describe where rope break occurs": "Describe where rope break occurs", "Select option to see other Wire Breaks": "Select option to see other Wire Breaks", "Please select user account": "Please select user account", "Please user industry": "Please user industry", "Chafe Information": "Chafe Information", "Enter Workorder Number": "Enter Workorder Number", "Enter Certificate No": "Enter Certificate No", "Create": "Create", "Search": "Search", "Product Details": "Product Details", "Product name": "Product Name", "Jacket Status": "Jacket Status", "Tap to see current external": "Tap to review details, make modifications, or to add pictures of the current external abrasion rating. If the abrasion rating has changed over the length of the rope, add a new external abrasion observation using the \"+\" icon at the bottom of the screen.", "Tap to see current internal": "Tap to review details, make modifications, or to add pictures of the current internal external abrasion rating. If the abrasion rating has changed over the length of the rope, add a new internal abrasion observation using the \"+\" icon at the bottom of the screen.", "Click to search inspection": "Click to search inspection", "Enter Work Order Number": "Enter Work Order Number", "Enter RFT": "Enter RFT", "Options": "Options", "Enter User Account": "Enter User Account", "Enter User Industry": "Enter User Industry", "Back": "Back", "Enter Product Details": "Enter Product Details", "is Jacketed": "is Jacketed", "Address": "Address", "Done": "Done", "Details taken.": "Details taken.", "Standard": "Standard", "Others": "Others", "To be implemented": "To be implemented", "<strong>You want to delete this Inspection</strong>!": "<strong>You want to delete this inspection</strong>!", "OK": "OK", "<strong>Unsaved changes are found do you want to go back</strong>!": "<strong>Unsaved changes are found do you want to go back</strong>!", "Okay": "Okay", "You want to reset this Inspection": "You want to reset this inspection", "Please fill all values to continue": "Please fill all values to continue", "Planned": "Planned", "Ad-hoc": "Ad-hoc", "Inspected length should be less than total length": "Inspected length should be less than total length", "Error while searching.": "Error while searching.", "Error while getting data from db": "Error while getting data from db", "Certificate": "Certificate", "Work Orders": "Work Orders", "Accounts": "Accounts", "You want to delete this measurement": "You want to delete this measurement", "Work Order": "Work Order", "Enter Work Order No": "Enter Work Order No", "Cert No": "Cert No", "Enter Certificate Number": "Enter Certificate Number", "Do you want to discard the changes and go back": "Do you want to discard the changes and go back", "Do you want to discard the changes and go to' + ' ' + value + ' '+'page": "Do you want to discard the changes and go to' + ' ' + value + ' '+'page", "Select the type of damage ": "Select the type of damage", "no observations recorded yet, tap on the + button to add one now.": "No observations recorded yet.  Tap on the + button to add one now.", "Copyright 2019 Samson Rope Technologies. Inc": "© 2020 Samson Rope Technologies Inc", "Account Id": "Account", "Area": "Affected Length", "Anomaly": "Anomaly", "Application": "Application", "Are you sure?": "Delete Image", "Area in Feet": "Length in Feet", "As a Guest": "Guest", "Asset Id": "<PERSON><PERSON>", "Basket Deformation": "Basket Deformation", "Bird Caging": "Birdcaging", "Broken yarns": "Broken yarns", "Calculation of total area loss": "Calculation of area loss caused by cut yarns", "Calculation of total area loss melting": "Calculation of area loss caused by melted yarns", "Calculation of total twists per foot": "Calculation of twists per foot", "Calculation of total yarns": "Calculation of total yarns in the rope", "Cancel": "Cancel", "Cert #": "Cert #", "Cert Number": "Certificate Number", "Cert": "Certificate Number", "Certificate No": "Certificate Number", "Chafe Type": "Chafe Type", "Cold Shrink": "Cold Shrink", "Color Other": "Color Other", "Color": "Color", "Complete": "Complete", "Completed": "Completed", "Configuration": "Configuration", "Confirm": "Confirm", "Construction": "Construction", "Contact us": "Contact", "Contact Us": "Contact Us", "Contact": "Contact Us", "Contamination type": "Contamination type", "Contamination": "Contamination", "Corrosion Measurement": "Corrosion", "Create Unique Identifier": "Enter Ad-Hoc Certificate Number", "Created By": "Created By", "Created Date": "Created Date", "Current Length": "Current Length", "Customer Service": "Customer Service", "Cuts Measurement": "Cuts Yarns", "Delete": "Delete", "You want to delete this Inspection": "Deleted inspections cannot be recovered.", "Describe type info": "Describe contamination", "Describe number of wire breaks occur": "Describe number of wire breaks occur", "Describe the discoloration type": "Select type", "Enter the discoloration type info": "Describe the discoloration type", "Describe the end value": "Enter value here", "Describe the number of wire breaks occurs": "Enter the number of broken wires", "Describe the type of melting": "Select whether the damage occurs to load bearing or non load bearing fibers. If the melting occurs on the jacket or chafe, select not load bearing. Otherwise, the damage is to the load bearing fiber.", "Description": "Description", "Diameter UOM": "Diameter UOM", "Diameter": "Diameter", "Hey, Difference between start & end value must not exceed 1 meter": "If the issue spans a length greater than two picks, please input it as a separate observation (for non-uniform damage) or as linear damage (for uniform damage).", "Dirt": "Dirt", "Discoloration": "Discoloration", "Do you know Certificate No?": "Do you know the certificate number?", "Do you know the product name?": "Do you know the product name?", "Do you know Workorder No?": "Do you know your work order number?", "Do you want to discard the changes and go back?": "All changes will be discarded. Are you sure you want to go back?", "Does the product have chafe?": "Does the product have chafe?", "Email": "Email", "Empty": "Empty", "Hey, End value must not exceed inspecting length which is": "The end position cannot be greater than the inspected length of the rope which was defined as ", "Hey, end value must not lesser than the start value": "The start position must not be greater than the end position", "Enter Inspected Length": "Enter length", "Enter installed location": "Enter location", "Enter no.of breaks occurs": "Number of breaks", "Enter no.of strands": "Number of strands", "Enter no of yarns": "Number of yarns", "Enter the discoloration type information": "Describe the nature of the discoloration (i.e. fading or heavily coated)", "Enter the length of caging": "Enter the distance over which the birdcaging occurs", "Enter the length of compression": "Enter the distance over which the compression occurs", "Enter the length of contamination": "Enter the distance over which the contamination occurs. If the severity level worsens over the length of the rope, add it as a separate observation.", "Enter the length of corrosion": "Enter the distance over which the corrosion occurs", "Enter the length of cuts": "Enter the distance over which the cuts occur. Enter a new observation if the cut damage spans a distance greater than two rope picks.", "Enter the length of deformation": "Enter the distance over which the deformation occurs", "Enter the length of discoloration": "Enter the distance over which the discoloration occurs. If the severity level worsens or contamination type changes over the length of the rope, add it as a separate observation.", "Enter the length of flattening": "Enter the distance over which the flattening occurs", "Enter the length of heat damage": "Enter the distance over which the heat damage occurs", "Enter the length of Inconsistent diameter": "Enter the distance over which the inconsistent diameter occurs", "Enter the length of kinking": "Enter the distance over which the kinked yarns appear. If the severity level worsens over the length of the rope, add it as a separate observation.", "Enter the length of issue": "Length of Observation", "Enter the length of parting": "Enter the length of the parted section starting at the first signs of broken fibers to the tip of the parted fibers.", "Enter the length of protusion": "Enter the length or rope affected by protrusion", "Enter the length of pulled": "Enter the length of rope that has been distorted from the pulled yarns or strands", "Enter the length of seizing": "Enter the length of Seizing", "Enter the length of twist": "Enter the length of rope with imparted twist. If the twist severity worsens over the length of the rope, add it as a separate observation.", "Enter the value of area in feet": "Enter the length affected by twist. If the twist severity increases over the length of the rope, record this as a separate observation.", "Enter the length of waviness": "Enter the length affected by waviness", "Enter the length of wire breaks": "Enter the length affected by wire breaks", "Enter the measurement starting point of caging": "Measure the distance from the starting point of the rope to the start of the birdcaging observation. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of compression": "Measure the distance from the starting point of the rope to the start of the compression observation. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of contamination": "Measure the distance from the starting point of the rope to the start of the contamination observation. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of corrosion": "Measure the distance from the starting point of the rope to the start of the corrosion observation. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of cuts": "Measure the distance from the starting point of the rope to the start of the cut yarns/strands observation. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of deformation": "Measure the distance from the starting point of the rope to the deformation in the rope. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of discoloration": "Measure the distance from the starting point of the rope to the start of the discoloration in the rope. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of external abrasion": "Measure the distance from the starting point of the rope to the observed change in external abrasion. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of flattening": "Measure the distance from the starting point of the rope to the start of the flattening observation. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of heat damage": "Measure the distance from the starting point of the rope to the start of the heat damage. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of Inconsistent diameter": "Measure the distance from the starting point of the rope to the start of the inconsistent diameter. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of internal abrasion": "Measure the distance from the starting point of the rope to the observed change in internal abrasion. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of kinking": "Measure the distance from the starting point of the rope to the start of the kinked yarns. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of parted": "Measure the distance from the starting point of the rope to the parted section of the rope. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of protusion": "Measure the distance from the starting point of the rope to the section of the rope with wire/strand/core protrusion. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of pulled": "Measure the distance from the starting point of the rope to the first sign of deformation caused by the pulled yarns/strands. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of seizing": "Measure the distance from the starting point of the rope to the seized section of the rope. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of twist": "Measure the distance from the starting point of the rope to the start of the twisted section of rope. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of waviness": "Measure the distance from the starting point of the rope to the wavy section of the rope. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of linear damage": "Measure the distance from the starting point of the rope to the damaged section of the rope. The measuring tape icon to the right can be used to help measure.", "Enter the measurement starting point of wire breaks": "Measure the distance from the starting point of the rope to the start of the section of the rope with wire breaks. The measuring tape icon to the right can be used to help measure.", "Enter the value of twists per feet": "Calculate twist per foot by dividing 1 by the length in feet over which the rope completes one turn about its axis.", "Describe the start value in meters": "Enter the start value", "Describe the start value": "Enter the start value in {{uomString}}", "Enter the measurement starting point value": "Starting Point of Observation", "Enter Total Length": "Enter Total Length", "Entire Rope": "<PERSON><PERSON><PERSON>", "Current External": "External Abrasion", "External Abrasion": "External Abrasion", "External Id": "External Id", "External Rating": "External Abrasion", "Fax": "Fax", "Flattening Measurement": "Flattening", "Full Parting": "Full Parting", "General Compression": "Compression", "General Condition": "Abrasion Rating", "Grease": "Grease", "Has Chafe": "<PERSON>", "Heat Damage": "Heat Damage", "Heat Shrink": "Heat Shrink", "Help Mode Disabled": "Help Mode Disabled", "Help Mode Enabled": "Help Mode Enabled", "Home": "Home", "In Progress": "In Progress", "Inconsistent Diameter": "Inconsistent Diameter", "Industry": "Industry", "Initial Condition": "Initial Condition", "Inspected length cannot be more than total length which is": "The inspected rope length cannot be longer than the total rope length which is", "Inspected Length": "Inspected Length", "Inspection Id": "Inspections Id", "Inspection length should be less than total length": "The inspected length must be less than the total rope length.", "Inspection Length": "Inspected Length", "Inspection Status": "Status", "Inspection": "Inspections", "Select Date Installed": "Installation Date", "Installed Date": "Installation Date", "Installed Status": "Installed Status", "Current Internal": "Internal Abrasion", "Internal Abrasion": "Internal Abrasion", "Internal Rating": "Internal Abrasion", "Is Jacketed": "Jacketed?", "Is the product jacketed?": "Is the product jacketed?", "Is this a Samson Product?": "Is this a Samson product?", "Is this part of planned maintainance?": "Is this a planned inspection?", "Is this product installed?": "Is the rope installed?", "Kinking Measurement": "Kinked Yarns", "kinking yarns": "Kinked Yarns", "Largest Diameter": "Increased Diameter", "Length of issue should be less than 1 Meter": "If the issue spans a length greater than two picks, please input it as a separate observation (for non-uniform damage) or as linear damage (for uniform damage).", "Length of issue should be less than 100 Centimeter": "If the issue spans a length greater than two picks, please input it as a separate observation (for non-uniform damage) or as linear damage (for uniform damage).", "Length of issue should be less than 1000 Millimeter": "If the issue spans a length greater than two picks, please input it as a separate observation (for non-uniform damage) or as linear damage (for uniform damage).", "Length of issue should be less than 3.28 Feet": "If the issue spans a length greater than two picks, please input it as a separate observation (for non-uniform damage) or as linear damage (for uniform damage).", "Length of issue should be less than 39.37 Inchs": "If the issue spans a length greater than two picks, please input it as a separate observation (for non-uniform damage) or as linear damage (for uniform damage).", "Length UOM": "Length UOM", "Parting Measurement": "Line Parting", "Line Position": "Line Position", "LineTracker": "LineTracker", "Load Bearing": "Load Bearing", "Location Installed": "Location", "Installed location": "Location of installation", "Login": "<PERSON><PERSON>", "Manufactorer": "Manufacturer", "Manufacturer": "Manufacturer", "Melting Measurement": "Melted Yarns", "Melting yarns": "Melted Yarns", "Menu": "<PERSON><PERSON>", "Message": "Message", "Mild": "Mild", "Moderate": "Moderate", "Modified By": "Modified By", "Modified Date": "Modified Date", "Multi Sided": "Multi - Sided", "Name": "Name", "New": "New", "Next": "Next", "No changes to save.": "No changes to save.", "No inspections found.": "No in progress inspections", "No measurements recorded.": "No measurements recorded.", "Hey, No observations recorded yet.Tap on the + button to add one now.": "No observations recorded yet. Select the + button to add inspection details.", "Hey, No observations recorded.": "No observations recorded yet.", "No Options found": "No observations recorded.", "No records found": "No options found", "No work order found.": "No records found", "No": "No", "None": "None", "Not Load Bearing": "Not Load Bearing", "Number of broken yarns should not be greater than total yarns which is": "The number of broken yarns cannot be greater than the total yarns which is", "No.of broken yarns": "Number of broken yarns", "Number of kinking yarns should not be greater than total yarns which is": "The number of kinked yarns cannot be greater than the total yarns which is", "No.of kinking yarns": "Number of kinked yarns", "No.of melting yarns": "Number of melted yarns", "No.of pulled yarns": "Number of pulled yarns", "Number of strands": "Number of strands", "Observations": "Observations", "Oil": "Oil", "Ok": "Ok", "One Side": "One Side", "Original Configuration": "Original Configuration", "Original Length": "Original Length", "Other Chafe": "Other Chafe", "Other": "Other", "Paint": "Paint", "Part of the rope": "Part of the rope", "Partial Parting": "Partial Parting", "Please fill all fields to continue": "Please complete required fields.", "Please fill all the fields to continue": "Please complete the required fields", "Please enter all mandatory fields": "Please complete the required fields", "Please enter certificate number to search": "Please enter the certificate number", "Please enter Certificate No": "Please enter certificate number to search", "Please enter mandatory fields.": "Please enter mandatory fields.", "Please Enter product details": "Please enter product details", "Please enter valid values to save": "Please enter valid values to save", "Please enter work order number to search": "Please enter work order number to search", "Please enter workorder No": "Please enter the work order number.", "Please enter workorder or certificate number to continue.": "Please enter the work order or certificate number to continue.", "Please enter your email": "Please enter your email", "Please enter your name": "Please enter your name", "Please select account to continue": "Please select account to continue", "Please select application type to continue": "Please select the application type to continue", "Please select industry to continue": "Please select industry to continue", "Please select manufacturer to continue": "Please select manufacturer to continue. If unknown or not listed, please select Other.", "Please select product type to continue": "Please select product type to continue", "Please set the rating": "Please select internal abrasion rating", "Please take a photo of inspecting rope": "Please take a photo of the observation prior to saving", "Please wait...": "Please wait...", "Product Code": "Product Code", "Product Color": "Product Color", "Product Configuration": "Product Configuration", "Product Description": "Product Description", "Product Desc": "Product Description", "Product Desceription": "Product Description", "Product Location": "Product Location", "Product Name": "Product Name", "Product Type": "Product Type", "Product": "Product", "Protusion Measurement": "Protrusion", "Pulled yarns": "Pulled yarns", "Radially Uniform": "Radially Uniform", "Recorded Observations": "Recorded Observations", "Recycle Bin is empty.": "Recycle bin is empty.", "Refresh Master Data": "Refresh Data", "Refreshing master data": "Refreshing master data", "Resource": "Resources", "Resources": "Resources", "Review": "Review", "RFT Number": "RFT Number", "RPS": "RPS", "Rust": "Rust", "Samson Employee": "Samson Employee", "Samson Inspections": "Samson Inspections", "Seizing Measurement": "Seizing", "Select Account": "Select Account", "Please select account": "Select Account", "Select application type": "Select the primary application for the rope", "Please select application type": "Select application type", "Select Certificate No": "Select Certificate Number", "Select Chafe Type": "Select Chafe Type", "Select compression type": "Select type of compression", "Select Configuration": "Select Configuration", "Select the Construction Type": "Select Construction Type", "Select kinking damage type": "Type of Damage", "Select damage type": "Make Selection", "Select Diameter": "Select Diameter", "Select external severity level": "Select external abrasion level", "Select inconsistent diameter type": "Select the type of inconsistent diameter", "Select user industry": "Select Industry", "Please select user industry": "Select Industry", "Select internal severity level": "Select internal abrasion level", "Select Manufacturer": "Select Manufacturer", "Select Observation": "Select Observation", "Select option to see other Corrosions": "Select damage type", "Select option to see other Heat Damages": "Select option to see other Heat Damages", "Select Product Color": "Select Product Color", "Select Product name from list": "Select Product From List", "Select Product Name": "Select Product Name", "Select product type": "Select Product Type", "Please select product type": "Select Product Type", "Select severity level": "Select Severity Level", "Select severity type": "Select Severity", "Select the cuts level": "Select the cuts level", "select the severity of the observation": "select the severity of the observation", "Select the discoloration severity level": "Mild = slight shift in hue or saturation, Moderate = obvious shift in hue or saturation, Severe = rope has been turned a different color or is faded to nearly white", "Select the external range of the rope": "Select the general external abrasion rating of the rope at the start of the inspection. As you continue down the line, this abrasion measurement can be updated.", "Select the internal range of the rope": "Select the general internal abrasion rating of the rope at the start of the inspection. As you continue down the line, this abrasion measurement can be updated.", "Select the kinking level": "Mild = light bends present on some yarns, Moderate = yarns are clearly bent, but not jagged, Severe = jagged kinking of yarns throughout the rope", "Select the discoloration level": "Mild = surface characteristic, Moderate = contamination is present between rope strands, Severe = contamination is present throughout the rope structure", "Select type of the compression": "Select the option that best describes the type of compression", "Select the pulled level": "Mild = yarns or strands are pulled with little to no disturbance to the rope structure", "Select the Severity level": "Severity Level", "Select the severity level": "Select the severity level", "Select the starting end of the rope": "End A/B is stated on the eye tag. If no tag is present and the product has not been end-for ended, A is the outboard eye. If the rope has already been end-for-ended, then B is the outboard eye.", "Select the type of damage": "Type of Damage", "Select the type of damage pulled": "Select whether the damage occurs to the load bearing or non load bearing fibers. If the pulled yarns or strands occur on the jacket or chafe, select not load bearing. Otherwise, the damage is to the load bearing fiber.", "Select kinking damage type help": "Select whether the damage occurs to the load bearing or non load bearing fibers. If the kinked yarns occur on the jacket or chafe, select not load bearing. Otherwise, the damage is to the load bearing fiber.", "Select the type of material used": "Select the type of material used", "Select the type of parting": "Select the type of parting", "Select parting type": "Select type", "Select type of the contamination": "Select type of contamination", "Select type of the damage": "Select type of contamination", "Select type1": "Select whether the damage occurs to load bearing or non load bearing fibers. If the kinked yarns occur on the jacket or chafe, select not load bearing. Otherwise, the damage is to the load bearing fiber.", "Select type": "Select type", "Select compression type placeholder": "Select Compression Type", "Select UOM": "Select UOM", "Select type of the rope inconsistent diameter": "Select whether the change in diameter is smaller or larger than the nominal rope diameter", "Select which part contamination occur": "Select which part of the rope corresponds to this observation. If damage occurs to more than one layer, please enter a separate observation for each layer.", "Select Workorder Number": "Select Work Order Number", "Send message": "Send Message", "Set the rating of external abrasion": "Set the external abrasion rating", "Set the rating of internal abrasion": "Set the internal abrasion rating", "Set the rating of the external abrasion": "None = no evidence of fraying", "Set the rating of the internal abrasion": "None = no evidence of fraying", "Settings": "Settings", "Severe": "Severe", "Single Side": "Single Side", "Smallest Diameter": "Decreased Diameter", "Hey, start & end value must not same": "The start and end position cannot be the same", "Hey, start & end value must not exceed inspecting length which is": "The start and end positions must not exceed the inspected length", "Hey, Start value must not exceed ending value": "The start position must not be greater than the end position", "Subject": "Subject", "Submitted Inspection": "Submitted Inspection", "Take a photo of rope external initial condition": "Take a photo of external initial condition", "Take a photo of rope internal initial condition": "Take a photo of general internal abrasion condition at the start of the inspection.", "Tap icon to take measurement length": "To use the measurement tool, tap the measuring tape icon, point the green \"+\" symbol at the starting point of the measurement and click the \"+\" sign at the bottom of the screen. Shift the camera to the end of the measurement, click the \"+\" sign at the bottom of the screen again, and click \"done\". The distance will automatically populate the measurement field.", "Tap to continue Inspection": "Tap to begin adding more observations to the inspection", "Tap to save basket deformation measurement": "Tap to save basket deformation observation", "Tap to save bird caging measurement": "Tap to save birdcaging observation", "Tap to save general compression measurement": "Tap to save compression observation", "Tap to save contamination measurement": "Tap to save contamination observation", "Tap to save corrosion measurement": "Tap to save corrosion observation", "Tap to save cuts measurement": "Tap to save cut yarns/strands observation", "Tap to save discoloration measurement": "Tap to save discoloration observation", "Tap to save external abrasion measurement": "Tap to save external abrasion observation", "Tap to save flattening measurement": "Tap to save flattening observation", "Tap to save heat damage measurement": "Tap to save heat damage observation", "Tap to save inconsistent diameter measurement": "Tap to save inconsistent diameter observation", "Tap to save internal abrasion measurement": "Tap to save internal abrasion observation", "Tap to save kinking measurement": "Tap to save kinked yarn observation", "Tap to save parting measurement": "Tap to save line parting observation", "Tap to save protusion measurement": "Tap to save protrusion observation", "Tap to save pulled measurement": "Tap to save pulled yarns/strands observation", "Tap to save seizing measurement": "Tap to save seizing measurement", "Tap to save twists measurement": "Tap to save twist observation", "Tap to save waviness measurement": "Tap to save waviness observation", "Tap to save wire breaks measurement": "Tap to save wire breaks observation", "Tap to save melting measurement": "Tap to save melting observation", "Tap to save linear damage measurement": "Tap to save linear damage observation", "Tap to start new measurement": "Tap to add new observation", "Tap to take a photo of basket deformation": "Tap to take a photo of basket deformation", "Tap to take a photo of bird caging": "Tap to take a photo of birdcaging", "Tap to take a photo of general compression": "Tap to take a photo of compression", "Tap to take a photo of contamination": "Tap to take a photo of contamination", "Tap to take a photo of corrosion": "Tap to take a photo of corrosion", "Tap to take a photo of cut": "Tap to take a photo of cut damage", "Tap to take a photo of rope discoloration": "Tap to take a photo of discolored section of rope", "Tap to take a photo of rope external condition": "Tap to take a photo of external abrasion condition", "Tap to take a photo of flattening": "Tap to take a photo of flattened section", "Tap to take a photo of heat damage": "Tap to take a photo of heat damage", "Tap to take a photo of inconsistent diameter": "Tap to take a photo of inconsistent diameter observation", "Tap to take a photo of rope internal condition": "Tap to take a photo of internal abrasion condition", "Tap to take a photo of kinking": "Tap to take a photo of kinked yarns", "Tap to take a photo of melting": "Tap to take a photo of melted fibers", "Tap to take a photo of parting": "Tap to take a photo of the parted section of rope", "Tap to take a photo of protusion": "Tap to take a photo of protruded wires, strands, or core", "Tap to take a photo of pulled": "Tap to take a photo of pulled yarns or strands", "Tap to take a photo of seizing": "Tap to take a photo of <PERSON><PERSON>", "Tap to take a photo of twist": "Tap to take a photo of imparted twist in the rope", "Tap to take a photo of waviness": "Tap to take a photo of waviness condition", "Tap to take a photo of wire breaks": "Tap to take a photo of wire breaks", "Tap to take a photo of linear damage": "Tap to take a photo of damage", "Total Area Loss": "Total Area Loss Percentage", "Total Length": "Total Length", "Total yarns": "Total yarns", "Twists": "Turns per [meter or ft]", "Tutorial": "Tutorial", "Twist Measurement": "Twist", "Twist per Foot": "Twist per Foot", "Twists per Feet": "Twists per foot", "Message for me": "Type message here", "Undo": "Undo", "UOM": "UOM", "User Id": "User ID", "Validation Error": "Validation Error", "Waviness Measurement": "Waviness", "Enter the length of melting": "Enter the distance over which the melting occur. Enter a new observation if the melting damage spans a distance greater than two rope picks.", "Enter the measurement starting point of melting": "Measure the distance from the starting point of the inspection to the melted section of the rope. The measuring tape icon to the right can be used to help measure.", "Which end will you be starting from?": "Which end will you be starting from?", "Select the rope melting type": "Which part of the rope is melted?", "Will the entire product be inspected now?": "Will the entire rope be inspected now?", "Wire Breaks": "Wire Breaks", "Workorder#": "Work Order Number", "Workorder Number": "Work Order Number", "Yarns per strand": "Yarns per strand", "Yes": "Yes", "You want to delete this image": "Are you sure you want to delete this image?", "INSPECTION_TYPE_TITLE": "Inspection Type", "INSPECTION_TYPE_QUESTION": "Is this a Samson scheduled Inspection?", "INSPECTION_SETUP_TITLE": "Inspection Setup", "INSPECTION_SETUP_WORK_ORDER_QUESTION": "Do you know your work order number?", "INSPECTION_SETUP_WORKORDER_INSTRUCTION": "You can find your Samson Work Order in Connect, under Maintenance Activities.", "INSPECTION_SETUP_CERT_NO_QUESTION": "Is your Certificate in the Samson System?", "INSPECTION_SETUP_UNIQUE_IDENTIFIER_LABEL": "Enter a Unique Identifier for the Rope", "INSPECTION_SETUP_UNIQUE_IDENTIFIER_PLACEHOLDER": "Unique Identifier", "INSPECTION_SETUP_INDUSTRY_LABEL": "Select Industry", "INSPECTION_SETUP_INDUSTRY_PLACEHOLDER": "Industry", "INSPECTION_SETUP_APPLICATION_LABEL": "Select Application", "INSPECTION_SETUP_APPLICATION_PLACEHOLDER": "Primary Application", "INSPECTION_SETUP_PRODUCT_NAME_LABEL": "Select Product Name", "INSPECTION_SETUP_PRODUCT_NAME_PLACEHOLDER": "Product Name", "INSPECTION_SETUP_DIAM_UNIT_LABEL": "Select Units", "INSPECTION_SETUP_DIAM_UNITS_PLACEHOLDER": "Units", "INSPECTION_SETUP_DIAM_LABEL": "Select Diameter", "INSPECTION_SETUP_DIAM_PLACEHOLDER": "Diameter", "INSPECTION_SETUP_CONSTRUCTION_CLASS_PLACEHOLDER": "Construction Class", "INSPECTION_SETUP_CONSTRUCTION_TYPE_PLACEHOLDER": "Construction Type", "INSPECTION_SETUP_NONSAMSON_PRODUCT_NAME_LABEL": "Enter Product Name", "INSPECTION_SETUP_NONSAMSON_PRODUCT_NAME_PLACEHOLDER": "Product Name", "INSPECTION_SETUP_NONSAMSON_PRODUCT_CODE_LABEL": "Enter Product Code", "INSPECTION_SETUP_NONSAMSON_PRODUCT_CODE_PLACEHOLDER": "Product Code", "INSPECTION_SETUP_NONSAMSON_PRODUCT_DESCRIPTION_LABEL": "Enter Product Description", "INSPECTION_SETUP_NONSAMSON_PRODUCT_DESCRIPTION_PLACEHOLDER": "Product Description", "INSPECTION_SETUP_NONSAMSON_LENGTH_UOM_LABEL": "Select Units", "INSPECTION_SETUP_NONSAMSON_LENGTH_UOM_PLACEHOLDER": "Units", "INSPECTION_SETUP_TOTAL_LENGTH_LABEL": "Enter Total Length", "INSPECTION_SETUP_TOTAL_LENGTH_PLACEHOLDER": "Total Length", "INSPECTION_SETUP_INSPECTED_LENGTH_LABEL": "Enter Inspected Length", "INSPECTION_SETUP_INSPECTED_LENGTH_PLACEHOLDER": "Inspected Length", "INSPECTION_SETUP_INSTALLED_LOCATION_LABEL": "What equipment is this installed on?", "INSPECTION_SETUP_INSTALLED_LOCATION_PLACEHOLDER": "Ex: Winch #1", "INSPECTION_SETUP_START_END_DESCRIPTION": "If your rope has Samson tags on each end, select which end you will begin the inspection. If there is no tag, select Other and describe.", "INSPECTION_SETUP_START_DESCRIPTION_LABEL": "Enter Start Description", "INSPECTION_SETUP_START_DESCRIPTION_PLACEHOLDER": "Describe the end you are starting from", "CONFIGURATION_TYPE_TITLE": "Rope Configuration", "CONFIGURATION_TYPE_QUESTION": "Would you like to build a new configuration or lookup an existing configuration?", "CONFIGURATION_TYPE_NEW_CONFIGURATION": "Build Configuration", "CONFIGURATION_TYPE_PREVIOUS_CONFIGURATION": "Lookup Configuration", "INSPECTION_CONFIGURATION_TITLE": "Rope Configuration", "INSPECTION_CONFIGURATION_OVERALL_LENGTH_LABEL": "Overall Length", "INSPECTION_CONFIGURATION_ZERO_DATA_TEXT": "No configuration measurements recorded yet. Select + button to add configuration details.", "END_CONFIG_DESCRIPTION": "How is the End Identified", "END_CONGIG_END_TYPE_LABEL": "Select End Type:", "END_CONGIG_END_TYPE_DESCRIPTION": "End Type", "END_CONFIG_CHAFE_TYPE_LABEL": "Select Chafe Type:", "END_CONFIG_CHAFE_TYPE_PLACEHOLDER": "Chafe Type", "END_CONFIG_CHAFE_TYPE_HARDWARE_LABEL": "Select Hardware Type:", "END_CONFIG_CHAFE_TYPE_HARDWARE_PLACEHOLDER": "Hardware Type", "CHAFE_CONFIG_FIXED_OR_SLIDING": "Is this Fixed or Sliding Chafe?", "SPLICE_CONFIG_TYPE_LABEL": "Select Splice Type:", "SPLICE_CONFIG_TYPE_PLACEHOLDER": "Splice Type", "SPLICE_CONFIG_EYE": "Is this an Eye Splice?", "OTHER_CONFIG_DESCRIOTION_LABEL": "Describe:", "OTHER_CONFIG_DESCRIOTION_PLACEHOLDER": "Description", "KINKING_CALCULATION_TITLE": "Total Yarns", "KINKING_STARANDS_PER_YARN_LABEL": "Enter Yarns per Strand", "KINKING_STARANDS_PER_YARN_PLACEHOLDER": "Number of Yarns", "KINKING_STARANDS_LABEL": "Enter Number of Strands", "KINKING_STARANDS_PLACEHOLDER": "Number of Strands", "KINKING_LOSS_CALCULATION_TITLE": "Yarns Affected", "KINKING_KINKED_YARNS_LABEL": "Enter Number of Kinked Yarns", "KINKING_KINKED_YARNS_PLACEHOLDER": "Number of Kinked Yarns", "KINKING_TOTAL_YARNS_LABEL": "Total Yarns", "KINKING_TOTAL_YARNS_PLACEHOLDER": "Total Yarns", "KINKING_AREA_EFFECTED": "Area Affected", "CUTS_CALCULATION_TITLE": "Total Yarns", "CUTS_STARANDS_PER_YARN_LABEL": "Enter Yarns per Strand", "CUTS_STARANDS_PER_YARN_PLACEHOLDER": "Number of Yarns", "CUTS_STARANDS_LABEL": "Enter Number of Strands", "CUTS_STARANDS_PLACEHOLDER": "Number of Strands", "CUTS_LOSS_CALCULATION_TITLE": "Yarns Affected", "CUTS_CUT_YARNS_LABEL": "Enter Number of Cut Yarns", "CUTS_CUT_YARNS_PLACEHOLDER": "Number of Cut Yarns", "CUTS_TOTAL_YARNS_LABEL": "Total Yarns", "CUTS_TOTAL_YARNS_PLACEHOLDER": "Total Yarns", "CUTS_AREA_EFFECTED": "Area Loss", "PULLED_CALCULATION_TITLE": "Total Yarns", "PULLED_STARANDS_PER_YARN_LABEL": "Enter Yarns per Strand", "PULLED_STARANDS_PER_YARN_PLACEHOLDER": "Number of Yarns", "PULLED_STARANDS_LABEL": "Enter Number of Strands", "PULLED_STARANDS_PLACEHOLDER": "Number of Strands", "PULLED_LOSS_CALCULATION_TITLE": "Yarns Affected", "PULLED_PULLED_YARNS_LABEL": "Enter Number of Pulled Yarns", "PULLED_PULLED_YARNS_PLACEHOLDER": "Number of Pulled Yarns", "PULLED_TOTAL_YARNS_LABEL": "Total Yarns", "PULLED_TOTAL_YARNS_PLACEHOLDER": "Total Yarns", "PULLED_AREA_EFFECTED": "Area Affected", "DISCOLORATION_SEVERITY_LEVEL_LABEL": "Select the Severity Level", "DISCOLORATION_DESCRIPTION_LABEL": " Describe the Discoloration", "DISCOLORATION_DESCRIPTION_PLACEHOLDER": "Discoloration Description", "COMPRESSION_TYPE_LABEL": "Select the Type of Compression", "COMPRESSION_TYPE_PLACEHOLDER": "Compression Type", "TWISTS_PER_UNIT": "Enter Twist per ", "TWIST_PER_UNIT_PLACEHOLEDR": "Enter Twist Level", "INCONSISTEN_DIAMETER_TYPE_LABEL": "Select Type of Inconsistent Diameter:", "INCONSISTEN_DIAMETER_TYPE_PLACEHOLDER": "Type of Inconsistent Diameter", "INCONSISTEN_DIAMETER_DIAM_LABEL": "Enter Measured Diameter", "INCONSISTEN_DIAMETER_DIAM_PLACEHOLDER": "Measured Diameter", "LINEAR_DAMAGE_SEVERITY_LEBEL": "Select the Severity Level:", "LINEAR_DAMAGE_TYPE": "Select Type of Linear Damage:", "CONTAMINATION_CONTAMINATION_TYPE_LABEL": "Select Contamination", "CONTAMINATION_AREA_AFFECTED_LABEL": "Select Area Affected", "CONTAMINATION_CONTAMINATION_TYPE_PLACEHOLDER": "Contamination", "CONTAMINATION_AREA_AFFECTED_PLACEHOLDER": "Area Affected", "CONTAMINATION_SEVERITY_LEVEL": "Select the Severity Level", "PARTING_TYPE_LABEL": "Select the Type of Parting", "PARTING_TYPE_PLACEHOLDER": "Parting Type", "MELTING_CALCULATION_TITLE": "Total Yarns", "MELTING_STARANDS_PER_YARN_LABEL": "Enter Yarns per Strand", "MELTING_STARANDS_PER_YARN_PLACEHOLDER": "Number of Yarns", "MELTING_STARANDS_LABEL": "Enter Number of Strands", "MELTING_STARANDS_PLACEHOLDER": "Number of Strands", "MELTING_LOSS_CALCULATION_TITLE": "Yarns Affected", "MELTING_PULLED_YARNS_LABEL": "Enter Estimated Affected Yarns", "MELTING_PULLED_YARNS_PLACEHOLDER": "Number of Affected Yarns", "MELTING_TOTAL_YARNS_LABEL": "Total Yarns", "MELTING_TOTAL_YARNS_PLACEHOLDER": "Total Yarns", "MELTING_AREA_EFFECTED": "Estimated Area Loss", "EXTERNAL_ABRATION_RATING": "Rate the External Abrasion", "INTERNAL_ABRATION_RATING": "Rate the Internal Abrasion", "OBSERVATION_LENGTH_LABEL": "Length of {{measurementName}}", "OBSERVATION_LENGTH_PLACEHOLDER": "Length Value", "OBSERVATION_START_LABEL": "Start Point", "OBSERVATION_START_PLACEHOLDER": "Start Value", "OBSERVATION_END_LABEL": "End Point", "OBSERVATION_END_PLACEHOLDER": "End Value", "OBSERVATION_NOTES": "Notes", "CUTS_TITLE": "Cut Yarns", "PULLED_TITLE": "Pulled Strands/Yarns", "CROPPING_TITLE": "Cropping", "TOTAL_WORKING_HOUR_LABEL": "Total Working hours", "TOTAL_WORKING_HOUR_PLACEHOLDER": "Enter Total Working Hours", "TOTAL_WORKING_OPERATIONS_PLACEHOLDER": "Enter Total Working Operations", "TOTAL_WORKING_OPERATIONS_LABEL": "Total Working Operations", "LENGTH_CROPPED_PLACEHOLDER": "Enter Length Cropped", "LENGTH_CROPPED_LABEL": "Length Cropped(m)", "END_CROPPED_LABEL": "End In Use", "END_CROPPED_PLACEHOLDER": "Select End In Use", "END_CROPPED_DESC_PLACEHOLDER": "Enter End In Use", "CROPPING_ASSET_LIST_LABEL": "Select Asset", "CROPPING_CERT_LIST_LABEL": "Select Certificate", "EVENT_DATE_LABEL": "Event Date", "INSPECTION_DATE_LABEL": "Inspection Date", "EVENT_DATE_PLACEHOLDER": "Select Event Date", "INSPECTION_DATE_PLACEHOLDER": "Select Inspection Date", "DAMAGE_TYPE_LABEL": "Damage Type", "DAMAGE_TYPE_PLACEHOLDER": "Enter Damage Type", "SHIP_SIDE_LABEL": "Ship Side", "SHIP_SIDE_PLACEHOLDER": "Enter Ship Side", "REPAIR_TITLE": "Repair", "DISTANCE_IN_EYE_PLACEHOLDER": "Enter Distance from Eye", "DISTANCE_IN_EYE_LABEL": "Distance from Eye", "END_IN_USE_PLACEHOLDER": "Enter End being Repaired", "END_IN_USE_LABEL": "End being Repaired", "REPAIR_TYPE_LABEL": "Repair Type", "REPAIR_TYPE_PLACEHOLDER": "Enter Repair Type", "END_FOR_END_TITLE": "End For End", "EQUIPMENT_INSP_TITLE": "Equipment Inspection", "EQUIPMENT_LIST_LABEL": "Select Equipment", "ROPE_CONTACT_LABEL": "Rope Contact", "ROPE_CONTACT_PLACEHOLDER": "Enter Rope Contact", "ROTATION_TITLE": "Rotation", "FROM_WINCH_LIST_LABEL": "From", "TO_WINCH_LIST_LABEL": "To", "FROM_WINCH_LIST_PLACEHOLDER": "Select From", "TO_WINCH_LIST_PLACEHOLDER": "Select To", "INSTALL_LINE_TITLE": "Install Line", "INSTALL_LINE_EQUIPMENT_LIST_LABEL": "Select Equipment", "REQUEST_NEW_LINE_TITLE": "Request New Line", "ALTERNATE_CERT_NUMBER_LABEL": "Alternate Certificate Number", "ALTERNATE_CERT_NUMBER_PLACEHOLDER": "Enter Alternate Certificate Number", "CERTIFICATE_DATE_LABEL": "Certificate Date", "MANUFACTURER_LABEL": "Manufacturer", "MANUFACTURER_PLACEHOLDER": "Enter Manufacturer", "MANUFACTURER_SELECT_PLACEHOLDER": "Select Manufacturer", "PRODUCT_NAME_LABEL": "Product Name", "PRODUCT_TYPE_LABEL": "Product Type", "PRODUCT_NAME_PLACEHOLDER": "Enter Product Name", "PRODUCT_TYPE_PLACEHOLDER": "Enter Product Type", "PRODUCT_DESC_LABEL": "Product Description", "PRODUCT_DESC_PLACEHOLDER": "Enter Product Description", "EQUIPMENT_INSPECTION_TITLE": "Equipment Insp", "SCORING_LABEL": "Scoring", "PITTING_LABEL": "Pitting/Rust", "MOBILITY_LABEL": "Free Mobility", "SURFACE_RATING_LABEL": "Surface Rating", "ASSET_ACTIVITY_TYPE_TITLE": "Line Usage", "ACTIVITY_TYPE_TITLE": "Select Entry Type", "ACTIVITY_ADHCO": "AdHoc Activity", "ACTIVITY_IOT": "Click to receive recent vessel movement.", "ACTIVITY_IOT_NOTE": "This feature will require connectivity to the internet", "ASSET_ACTIVITY_TITLE": "Asset Activity", "ASSET_ACTIVITY_ACCOUNT_LIST_LABEL": "Account", "PORT_COUNTRY_PLACEHOLDER": "Enter Port Country", "PORT_COUNTRY_LABEL": "Port Country", "PORT_NAME_PLACEHOLDER": "Enter Port Name", "PORT_NAME_LABEL": "Port Name", "PORT_ENTERED_PLACEHOLDER": "Select Port Entered Date", "PORT_ENTERED_LABEL": "Port Entered", "PORT_EXITED_LABEL": "Port Exited", "PORT_EXITED_PLACEHOLDER": "Select Port Exited Date", "SEVERE_WEATHER_CONDITION_LABEL": "Severe Weather Condition", "SEVERE_LOADING_CONDITION_LABEL": "Notable Environmental Conditions", "AA_CONFIGURATION_TYPE_TITLE": "Lines Used", "AA_CONFIGURATION_WINCH_TITLE": "New Pattern", "AA_CONFIGURATION_WINCH_TITLE_WORKBOAT": "Line Selection", "AA_CONFIGURATION_PORT_TITLE": "Port Details", "LINE_INFORMATION_TITLE": "Line Information", "BEND_ANGLE_PLACEHOLDER": "<PERSON>ter Bend <PERSON>", "BEND_ANGLE_LABEL": "Bend <PERSON>le", "ESTIMATED_LINE_LENGTH_PLACEHOLDER": "Enter Estimated Line Length Used", "ESTIMATED_LINE_LENGTH_LABEL": "Estimated Line Length Used", "TAIL_LENGTH_PLACEHOLDER": "Enter <PERSON>l <PERSON>", "TAIL_LENGTH_LABEL": "Tail Length", "MAIN_LINE_LABEL": "Main Line", "MAIN_LINE_PLACEHOLDER": "Enter Main Line", "CERT_DATE_PLACEHOLDER": "Select Cert Date", "MANUAL_ENTRY": "Manual Entry", "AIS_ENTRY": "Automatic Entry", "END_OF_JOB": "End of Job", "START_OF_JOB": "Start of Job", "END_OF_JOB_PLACEHOLDER": "Select End of Job", "START_OF_JOB_PLACEHOLDER": "Select Start of Job", "LOCATION": "Location", "COUNTRY": "Country", "COUNTRY_PLACEHOLDER": "Select Country", "LOCATION_PLACEHOLDER": "Enter Location", "GENERAL_LINE_USAGE_TITLE": "Line Usage", "AVERAGE_LOAD_LABEL": "Average Load", "PEAK_LOAD_LABEL": "Peak Load", "NOTES_LABEL": "Notes", "WINCHES_END_TYPE_LABEL": "Outboard End in Use", "WINCHES_END_TYPE_PLACEHOLDER": "Select Outboard End in Use", "WINCHES_TAIL_LENGTH_LABEL": "Tail Length", "WINCHES_CERT_TAIL_LENGTH_LABEL": "Line Length", "WINCHES_TAIL_LENGTH_PLACEHOLDER": "Select Tail Length", "WINCHES_CERTIFICATE_LABEL": "Other Tail", "WINCHES_CERTIFICATE_PLACEHOLDER": "Select Other Tail", "WINCHES_ESTIMATED_LINE_LENGTH_LABEL": "Estimated Line Length Used", "WINCHES_AVERAGE_LOAD": "Average Load (kN)", "WINCHES_PEAK_LOAD": "Peak Load (kN)", "WINCHES_NOTES_LABEL": "Notes", "HELP_TEXT_CROPPING_WORKING_HOUR": "Please fill in the amount of working hours the line at time of event", "HELP_TEXT_CROPPING_WORKING_OPERATIONS": "Please fill in the number of operations the line has at time of event", "HELP_TEXT_CROPPING_LENGTH_CROPPED": "Document the length of the line that was cropped. This will update the length of the line in the \"Inventory\" section of the app.", "HELP_TEXT_CROPPING_END_IN_USE": "New Samson eye tags will have A and B printed on them. Select \"A\" if the line has not been end-for-ended. Select \"B\" if the line has been end-for-ended. Select \"Other\" to enter manual end entry. ", "HELP_TEXT_CROPPING_EVENT_DATE": "Enter the date the event took place. ", "HELP_TEXT_CROPPING_INSPECTION_DATE": "Enter the date the inspection is being done.", "HELP_TEXT_REPAIR_WORKING_HOUR": "Please fill in the amount of working hours the line at time of event ", "HELP_TEXT_REPAIR_WORKING_OPERATIONS": "Please fill in the number of operations the line has at time of event ", "HELP_TEXT_REPAIR_END_IN_USE": "New Samson eye tags will have A and B printed on them. Select \"A\" if the line has not been end-for-ended. Select \"B\" if the line has been end-for-ended. Select \"Other\" to enter manual end entry. ", "HELP_TEXT_REPAIR_DISTANCE_FROM_END": "Measure and enter the distance from the end (i.e. Eye) that the damage is located. ", "HELP_TEXT_REPAIR_REPAIR_TYPE": "Select the type of repair that was performed. ", "HELP_TEXT_REPAIR_DAMAGE_TYPE": "Select type of damage that caused the repair.", "HELP_TEXT_REPAIR_EVENT_DATE": "Enter the date the event took place. ", "HELP_TEXT_EQUIP_INSP_ROPE_CONTACT_SURFACE_RATING": "Please inspect and report the surface rating of the hardware/equipment that the line is coming into contact with the most. Use Samsons surface comparator if available. ", "HELP_TEXT_EQUIP_INSP_FLANGE_SURFACE_RATING": "The the line is operating on a winch, inspect and report the surface rating of the winch flange wall using Samson surface comparator. ", "HELP_TEXT_EQUIP_INSP_MOBILITY": "If equipment is mobile (i.e. Universal Fairleads, Pedestal Rollers) and is moving freely click \"Yes\". If the equipment is seized or stalled select \"No\". ", "HELP_TEXT_EQUIP_INSP_PITTING": "If pitting or rust is present on equipment, select \"Yes\". If equipment is free of pitting and rust select \"No\". ", "HELP_TEXT_EQUIP_INSP_SCORING": "If scoring is present on equipment, select \"Yes\". If equipment is free of scoring  select \"No\". ", "HELP_TEXT_EQUIP_INSP_EVENT_DATE": "Enter the date the event took place.", "HELP_TEXT_ENE_FOR_END_WORKING_HOUR": "Please fill in the amount of working hours the line at time of event ", "HELP_TEXT_END_FOR_END_WORKING_OPERATIONS": "Please fill in the number of operations the line has at time of event ", "HELP_TEXT_END_FOR_END_EVENT_DATE": "Enter the date the event took place. ", "HELP_TEXT_ROTATION_WORKING_HOUR": "Please fill in the amount of working hours the line at time of event ", "HELP_TEXT_ROTATION_WORKING_OPERATION": "Please fill in the number of operations the line has at time of event ", "HELP_TEXT_ROTATION_FROM_WINCH": "Select the equipment that the line is rotating from. ", "HELP_TEXT_ROTATION_TO_WINCH": "Select the equipment that the line is rotating  to. ", "HELP_TEXT_ROTATION_EVENT_DATE": "Enter the date the event took place. ", "HELP_TEXT_ASSET_ACTIVITY_ASSET_LIST": "Select the asset the lines were used with in operation.", "HELP_TEXT_ASSET_ACTIVITY_PORT_COUNTRY": "Select the Country the lines are being used in.", "HELP_TEXT_ASSET_ACTIVITY_PORT_NAME": "Type in the name of the port/terminal being visited. If it is a port/terminal that has been visited before, please use same name as previously used. A history of port visits can be found on the green “Rope History” tab.", "HELP_TEXT_ASSET_ACTIVITY_WEATHER_CONDITION_OTHER": "Severe loading conditions are anything that may have caused damage to the mooring lines. Please select “TRUE” if you believe the loading conditions on the line were severe and descibe the condition.", "HELP_TEXT_ASSET_ACTIVITY_PORT_ENTERED": "Fill in the date that the lines started the operation being captured. ", "HELP_TEXT_ASSET_ACTIVITY_PORT_EXITED": "Fill in the date that the lines ended the operation being captured. ", "HELP_TEXT_ASSET_ACTIVITY_PORT_TYPE": "Select one of the values in the list that best describes the port type.", "HELP_TEXT_ASSET_ACTIVITY_SHIP_SIDE": "Select the ship side the vessel moored on.", "HELP_TEXT_ASSET_ACTIVITY_WEATHER_CONDITION": "Severe loading conditions are anything that may have caused damage to the mooring lines. Please select “TRUE” if you believe the loading conditions on the line were severe.", "HELP_TEXT_WINCHES_END_TYPE": "This column captures what end of the line is being used. Select “A” if the line has not been end-for-ended (“Reversed”). Select “B” is the line has been end-for-ended.", "HELP_TEXT_WINCHES_TAIL_LENGTH": "Please select the length of the secondary line (i.e. Mooring tail, pennant, sling, soft shackle) on the equipment selected. ", "HELP_TEXT_WINCHES_CERTIFICATE": "If the certificate populated is not the current line in use, please select the correct certificate by choosing from the drop-down list of your line inventory. ", "HELP_TEXT_WINCHES_ESTIMATED_LENGTH": "Enter the estimated total working length of the main line used on this equipment. ", "HELP_TEXT_WINCHES_AVERAGE_LOAD": "When data is available, enter the average load the line received during mooring operations.", "HELP_TEXT_WINCHES_PEAK_LOAD": "When data is available, enter the peak load the line received during mooring operations.", "HELP_TEXT_WINCHES_NOTE": "Add any notes pertaining to the line, deck hardware, or environmental conditions.", "HELP_TEXT_REQUEST_NEW_LINE_ALTERNATE_CERT": "If the certificate number that is intended to be placed in service is not found in the list above, please provide the cerficate number here. Then proceed filling out the following fields. ", "HELP_TEXT_REQUEST_NEW_LINE_MANUFACTURER": "Enter the manufacture of the line (i.e. Samson, Bridon, Lankhourst).", "HELP_TEXT_REQUEST_NEW_LINE_PRODUCT_NAME": "Enter the Name of the Product (i.e. Amsteel-Blue, Euroflex).", "HELP_TEXT_REQUEST_NEW_LINE_PRODUCT_TYPE": "Enter the Type of the Product.", "HELP_TEXT_REQUEST_NEW_LINE_PRODUCT_DESC": "Enter any configuration details worth noting (i.e. 2 meter eye each end). ", "HELP_TEXT_REQUEST_NEW_LINE_SIZE_INCH": "Enter the product diameter in inches if applicable. ", "HELP_TEXT_REQUEST_NEW_LINE_SIZE_MM": "Enter the product diameter in millimeters. ", "HELP_TEXT_REQUEST_NEW_LINE_LENGTH_M": "Enter the overall length of the line in meters. ", "HELP_TEXT_REQUEST_NEW_LINE_STRENGTH": "Enter the LDBF or TDBF of the line. If these values are not available, please enter the minimum strength of the line. ", "HELP_TEXT_REQUEST_NEW_LINE_LINEAR_DENSITY": "This value should be supplied on MEG4 certificates and MEG4 Base Design Certificates. It can also be calculated by dividing the weight of the line (kg) by the length in meters. ", "HELP_TEXT_REQUEST_NEW_LINE_CONSTRUCTION": "Enter the construction of the line (i.e. 12-Strand, Jacketed, 8-Strand).", "HELP_TEXT_REQUEST_NEW_LINE_MATERIAL": "Enter the Material that the line is made of (i.e. HMPE, Nylon, Wire). ", "HELP_TEXT_REQUEST_NEW_LINE_CERT_DATE": "Enter the date that the certificate for the line was printed. ", "HELP_TEXT_REQUEST_NEW_LINE_EVENT_DATE": "Enter the date the event took place. ", "HELP_TEXT_INSPECTION_SETUP_APPLICATION_LABEL": "Enter the application of the line", "REQUEST_NEW_LINE_SIZE_IN_INCH": "<PERSON><PERSON>(in)", "REQUEST_NEW_LINE_SIZE_IN_MILLIMETER": "Size(mm)", "REQUEST_NEW_LINE_LENGTH_IN_METER": "Length(m)", "REQUEST_NEW_LINE_STRENGTH": "Strength (LDBF/TDBF)", "REQUEST_NEW_LINE_LINEAR_DENSITY": "Linear Density", "REQUEST_NEW_LINE_CONSTRUCTION": "Construction", "REQUEST_NEW_LINE_MATERIAL": "Material", "INSPECTION_SETUP_APPLICATION": "Application", "REQUEST_NEW_LINE_SIZE_IN_INCH_PLACEHOLDER": "<PERSON><PERSON>(in)", "REQUEST_NEW_LINE_SIZE_IN_MILLIMETER_PLACEHOLDER": "Enter Size(mm)", "REQUEST_NEW_LINE_LENGTH_IN_METER_PLACEHOLDER": "Enter Length(m)", "REQUEST_NEW_LINE_STRENGTH_PLACEHOLDER": "Enter Strength (LDBF/TDBF)", "REQUEST_NEW_LINE_LINEAR_DENSITY_PLACEHOLDER": "Enter Linear Density", "REQUEST_NEW_LINE_CONSTRUCTION_PLACEHOLDER": "Enter Construction", "REQUEST_NEW_LINE_MATERIAL_PLACEHOLDER": "Enter Material", "INSPECTION_SETUP_APPLICATION_LABEL_PLACEHOLDER": "Enter Application", "ZONE_ONE_LABEL": "Zone 1", "ZONE_TWO_LABEL": "Zone 2", "SMALLEST_VISIBLE_DIAMETER_LABEL": "Smallest Visible Diameter", "SMALLEST_VISIBLE_DIAMETER_PLACEHOLDER": "Enter smallest visible diameter", "LARGEST_VISIBLE_DIAMETER_LABEL": "Largest Visible Diameter", "LARGEST_VISIBLE_DIAMETER_PLACEHOLDER": "Enter largest visible diameter", "CUT_STRAND_COUNT_COVER_LABEL": "Cut Strand Count Cover", "CUT_STRAND_COUNT_COVER_PLACEHOLDER": "Enter cut strand count cover", "TOTAL_WORKING_HOURS_LABEL": "Total working hour", "TOTAL_WORKING_HOURS_PLACEHOLDER": "Enter total working hour", "TOTAL_OPERATIONS_LABEL": "Total of Operations", "TOTAL_OPERATIONS_PLACEHOLDER": "Enter total of operations", "PASS_FAIL_LABEL": "Pass/Fail", "NUMBER_OF_JACKETS_RUPTURE_LABEL": "Number Of Jackets Ruptures", "NUMBER_OF_JACKETS_RUPTURE_PLACEHOLDER": "Enter number of jackets ruptures", "NUMBER_OF_JACKETS_REPAIRS_LABEL": "Number of Jackets Repairs", "NUMBER_OF_JACKETS_REPAIRS_PLACEHOLDER": "Enter number of jacket repairs", "JACKETED_ERROR_MESSAGE": "*Samson Recommends Performing a Detailed Inspection of this Line", "MAX_WIRE_BREAKES": "Max Wire Breaks in 6 Diameters", "WAVINESS_GAP_MEASUREMENT": "Waviness Gap Measurement", "WIRE_BREAKS_IN_TERMIANTION": "Wire Breaks in Termination", "AA_ROUTINE_INSPECTION_TITLE": "Routine Inspection", "INSPECTION_END_IN_USE": "End In Use", "ANOMOLY_LABEL": "Anomalies", "EXTERNAL_ABRASION_PLACEHOLDER": "External Abrasion", "INTERNAL_ABRASION_PLACEHOLDER": "Internal Abrasion", "CUT_YARN_COUNT_PLACEHOLDER": "Cut Yarns Count", "LENGTH_OF_GLAZING_PLACEHOLDER": "Length Of Glazing", "TWIST_PER_METER_PLACEHOLDER": "Twist <PERSON>", "CHAFE_GEAR_HOLE_COUNT_PLACEHOLDER": "Chafe Gear Hole Count", "GUEST_MESSAGE": "Dear valued customers, Samson will be releasing a new state of the art inspection feature soon. Stay tuned for further updates from Samson.", "Retire end in use": "Damage identified requires rope to be repaired or retired.", "No action needed": "Damage identified is acceptable for continued use.", "Contact manufacturer for guidance": "Damage identified may require additional attention; Continue use or Repair.", "CERT_NAME": "Certificate Num", "LMD_TYPE": "LMD Type", "INSPECTION_DATE": "Inspection Date", "END_IN_USE": "End in Use", "WINCH_NAME": "Winch ID", "ADDITIONAL_NUMBER_OF_JACKETS_REPAIR": "Number of Jackets Repair", "ADDITIONAL_NUMBER_OF_JACKETS_RUPTURES": "Number of Jackets Ruptures", "EXTRA_PASS_FAIL": "Pass/Fail", "EXTRA_TOTAL_OF_OPERATION": "Total Operation", "EXTRA_TOTAL_WORKING_HOURS": "Working Hours", "ZONE_ONE_SMALLEST_VISIBLE_DIAM": "Smallest Visible Diameter(Z1)", "ZONE_ONE_LARGEST_VISIBLE_DIAM": "Largest Visible Diameter(Z1)", "ZONE_ONE_CUT_STRAND_COUNT_COVER": "Cut Strand Count Cover(Z1)", "ZONE_ONE_WIRE_BREAKS_IN_TERMIANTION": "Wire Breaks in Termination(Z1)", "ZONE_ONE_WAVINESS_GAP_MEASUREMENT": "Waviness Gap Measurement(Z1)", "ZONE_ONE_MAX_WIRE_BREAKES": "Max Wire Breaks(Z1)", "ZONE_ONE_EXTERNAL_ABRASION": "Exteral Abrasion(Z1)", "ZONE_ONE_INTERNAL_ABRASION": "Internal Abrasion(Z1)", "ZONE_ONE_CUT_YARN_COUNT": "Cut Yarn Count(Z1)", "ZONE_ONE_LENGTH_OF_GLAZING": "Length of Glazing(Z1)", "ZONE_TWO_SMALLEST_VISIBLE_DIAM": "Smallest Visible Diameter(Z2)", "ZONE_TWO_LARGEST_VISIBLE_DIAM": "Largest Visible Diameter(Z2)", "ZONE_TWO_CUT_STRAND_COUNT_COVER": "Cut Strand Count Cover(Z2)", "ZONE_TWO_WAVINESS_GAP_MEASUREMENT": "Waviness Gap Measurement(Z2)", "ZONE_TWO_WIRE_BREAKS_IN_TERMIANTION": "Wire Breaks in Termination(Z2)", "ZONE_TWO_EXTERNAL_ABRASION": "External Abrasion(Z2)", "ZONE_TWO_INTERNAL_ABRASION": "Internal Abrasion(Z2)", "ZONE_TWO_CUT_YARN_COUNT": "Cut Yarn Count(Z2)", "ZONE_TWO_LENGTH_OF_GLAZING": "Length of Glazing(Z2)", "ANOMOLIES_VALLEY_WIRE_BREAKS_IN_6_DIAM": "Valley Wire Breaks in 6 Diameter", "ANOMOLIES_WIRE_BREAKS_IN_30_DIAM": "Wire Breaks in 30 Diameter", "ANOMOLIES_EXTERNAL_COROSION": "External Corosion", "TWIST_PER_METER": "Twist <PERSON>", "CHAFE_GEAR_HOLE_COUNT": "Chafe Gear Hole Count", "TAIL_BEARING_POINT_CONNECTION": "Bearing Point Connection", "TAIL_FULLY_CUT_STRANDS": "Fully Cut Strands", "NOTES": "Notes", "ASSET_NAME": "<PERSON><PERSON>", "CONSTRUCTION_TYPE": "Construction Type", "CUSTOM_CONSTRUCTION": "Inspection Type", "APPLICATION": "Application", "DIAM": "Diameter", "SHIP_SIDE": "Ship Side", "INSTALLED_LOCATION": "Installed Location", "RPS_NAME": "RPS", "PRODUCT": "Product", "DIAM_UOM": "Diameter UOM", "HELP_TEXT_WINCHES_PRE-DEPLOYMENT_INSPECTION": "Select this if pre deployment inspection is completed", "PRE_DEPLOYMENT_INSPECTION_COMPLETED_LABEL": "Pre-deployment Inspection Completed", "info": "Info", "Line_Information_Not_Available": "Line Information Not Available", "Tenex_External_Abrasion": "Tenex External Abrasion", "date_time_entred": "Date/Time Entered:", "date_time_entred_workboat": "Start of Job:", "date_time_Entered_utility": "Date/Time Job Started:", "date_time_exited": "Date/Time Exited:", "date_time_exited_workboat": "End of Job:", "date_time_exited_utility": "Date/Time Job Ended:", "total_hours_moored": "Total Hours Moored:", "total_hours_moored_utility": "Total Hours:", "total_hours_moored_workboat": "Time of Job:", "New_Mooring_Pattern_Summary": "New Mooring Pattern Summary", "New_Workboat_Summary": "New Workboat Usage Summary", "New_Utility_Summary": "New Job Summary", "required": "Required", "Please enter valid customer work order number": "Please enter valid customer work order number", "Character Limit Exceeded": "Character Limit Exceeded (<PERSON> {{ limit }})", "Only letters and numbers are allowed": "Only letters and numbers are allowed", "letters numbers and symbols allowed": "Only letters,numbers and symbols are allowed", "Please fill the mandatory fields to continue": "Please fill the mandatory fields for {{ item }} to continue", "Fuse": "<PERSON><PERSON>", "routine_inspection_incomplete_data_error_msg": "Cannot Inspect Line, Please contact <PERSON> for assistance", "Insight-quick-inspect": "Insight Quick Inspect", "Rope-Image-Detection": "Rope Image Detection", "Add": "Add {{ configType }}", "Rope End": "Rope End", "EyeA": "Eye A", "EyeB": "Eye B", "SpliceA": "Splice A", "SpliceB": "Splice B", "Chafe": "<PERSON><PERSON>", "Blunt Cut/Bitter End": "Blunt Cut/Bitter End", "Spliced Eye w/Hardware": "Hardware", "Parted End/Break": "Parted End/Break", "Cow Hitched": "<PERSON><PERSON> Hitched", "Equipments": "Equipment"}