import { Injectable } from '@angular/core';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { Platform } from '@ionic/angular';
import { UtilserviceService } from './utilservice.service';
import {  UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { INPUT_CREATE_CASE_ATTACHMENT } from '../../models/INPUT_CREATE_CASE_ATTACHMENT';
import { PlatformService } from './platform.service';

@Injectable({
  providedIn: 'root'
})
export class FileAttachmentService {
  platformId: string = this.platformService.getPlatformId();
  constructor(private device: Device,
    private platform: Platform,
    private unviredCordovaSDK: UnviredCordovaSDK,
    private platformService: PlatformService) { }

  async createAttachmentItem(lid: string, file: any) {
    var _self = this;
    var imageData = file.url
    if (_self.platformId == 'electron') {
      if (file.type == 'image/png' || file.type == 'image/jpeg' || file.type == 'image.jpg') {
        imageData = file.url.changingThisBreaksApplicationSecurity
      }
    } else if (_self.device.platform == 'browser') {

    } else {
      if (file.type == 'image/png' || file.type == 'image/jpeg' || file.type == 'image.jpg') {
        imageData = file.url.changingThisBreaksApplicationSecurity.substr(file.url.changingThisBreaksApplicationSecurity.indexOf('_app_file_') + 10, file.url.changingThisBreaksApplicationSecurity.length - 1)
        if (_self.platform.is("android")) {
          if (imageData.indexOf('_app_file_') !== -1) {
            imageData = imageData.substring(imageData.indexOf('_app_file_') + 10)
          }
          imageData = "file://" + imageData;
        } else {
          imageData = "file:///" + imageData;
        }
      }
    }
    console.log('File name extraction, Substring');
    var fileName = '';
    if (_self.device.platform != 'browser') {
      fileName = imageData.substring(imageData.lastIndexOf('/') + 1)
    } else {
      fileName = file.title;
    }
    console.log('File name: ', fileName);
    var attachmentObject = new INPUT_CREATE_CASE_ATTACHMENT();
    attachmentObject.FID = lid
    attachmentObject.UID = UtilserviceService.guid();
    attachmentObject.EXTERNAL_URL = "";
    attachmentObject.FILE_NAME = fileName;
    attachmentObject.LOCAL_PATH = (_self.platformId == 'electron') ? imageData : imageData.substring(7, imageData.length);;
    console.log("Electron file location url: ", imageData.substring(12, imageData.length))
    console.log("localpath", attachmentObject.LOCAL_PATH);
    console.log("ATTACHMENT OBJECT" + JSON.stringify(attachmentObject))
    if (this.device.platform == 'browser') {
      attachmentObject.EXTERNAL_URL = file.url;
      attachmentObject.LOCAL_PATH = '';
      this.unviredCordovaSDK.dbInsertOrUpdate("INPUT_CREATE_CASE_ATTACHMENT", attachmentObject, false)
      return {
        "data": [{
          "fields": attachmentObject
        }]
      }
    } else {
      return await _self.unviredCordovaSDK.createAttachmentItem("INPUT_CREATE_CASE_ATTACHMENT", attachmentObject)
    }
  }
}
