<ion-header>
  <ion-toolbar>
    <ion-title>{{'External Abrasion' | translate}}</ion-title>
    @if(!isEmpEditConfig && (!isLoggedInUserEmployee || readFlag === 'readOnly')) {
        <!-- ! show Back button when this component is navigated to, for customer login or when viewing completed inspections-->
        <ion-buttons slot="start">
          <ion-back-button style="--color: #0057b3;" (click)="backButtonClicked()"></ion-back-button>
        </ion-buttons>
     } @else if (isEmpEditConfig) {
        <!-- ! showing Cancel button when this component is opened as a modal, for employee login -->
        <ion-buttons slot="secondary">
          <ion-button (click)="closeSpliceModal()" *ngIf="isEmpEditConfig">Cancel</ion-button>
        </ion-buttons>
     }
    
    <ion-buttons slot="primary">
      <ion-button color="primary" (click)="historyPopover($event)" class="help-button-style">
        <fa-icon class="icon-style-help" icon="list"></fa-icon>
      </ion-button>
      <ion-button color="primary" (click)="helpService.switchMode()" class="help-button-style">
        <fa-icon class="icon-style-help" *ngIf='helpService.helpMode' icon="times"></fa-icon>
        <fa-icon class="icon-style-help" *ngIf='!helpService.helpMode' icon="circle-info"></fa-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div style="padding:15px 10px 0px 17px">
    <label>{{'OBSERVATION_START_LABEL' | translate}}: <span style="color:rgb(221, 82, 82);font-size:15px;"> *
      </span></label>
    <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div tooltip="{{'Enter the measurement starting point'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers style="width:100%">
        <div [formGroup]="startForm">
          <mat-form-field style="width:100%">
            <input matInput type="text" [disabled]='true' [(ngModel)]="measurementStart" maxlength="18"
              placeholder="{{'OBSERVATION_START_PLACEHOLDER' | translate}}"
              (keydown)="keyPressed($event, measurementStart, 'measurementStart')" step="any" inputmode="decimal"
              [required] formControlName="helpMeasurementStartCtrl">
          </mat-form-field>
        </div>
      </div>
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
      <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'" tooltip="{{'Tap icon to take measurement length'|translate}}" positionV="bottom"
        [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
        <fa-icon class="icon-style" icon="tape" style="float:right" class="fa-2x" style="color: #666 !important;"
          (click)="(helpService.helpMode || readOnly) ? test() : takeMeasurement('start')"></fa-icon>
      </div>
    </div>

    <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div style="width:100%">
        <div [formGroup]="startForm">
          <mat-form-field style="width:100%">
            <input matInput type="text" [disabled]='true' [(ngModel)]="measurementStart" maxlength="18"
              placeholder="{{'OBSERVATION_START_PLACEHOLDER' | translate}}" (change)="onChangeDisable('start')"
              (keydown)="keyPressed($event, measurementStart, 'measurementStart')" step="any" inputmode="decimal"
              [required] formControlName="measurementStartCtrl">
            <mat-error *ngIf="hasErrorStart('start')">{{startErrorMessage}}</mat-error>
          </mat-form-field>
        </div>
      </div>&nbsp;
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
      <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'">
        <fa-icon class="icon-style" icon="tape" style="float:right" class="fa-2x" style="color: #666 !important;"
          (click)="(helpService.helpMode || readOnly) ? test() : takeMeasurement('start')"></fa-icon>
      </div>
    </div>
  </div>

  <div style="padding-top: 15px;">
    <ion-segment [disabled]='helpService.helpMode' [(ngModel)]="fieldSegment"
      (ionChange)="helpService.segmentChanged($event)" class="ion-segment-style" mode="ios"
      expand="block">
      <ion-segment-button value="length" mode="ios" class="ion-segment-button-style">
        <ion-label>{{'Length'| translate}}</ion-label>
      </ion-segment-button>
      <ion-segment-button value="end" mode="ios" class="ion-segment-button-style">
        <ion-label>{{'End' | translate}}</ion-label>
      </ion-segment-button>
    </ion-segment>
  </div>

  <div style="padding:15px 10px 0px 17px" *ngIf="fieldSegment == 'length'">
    <label>{{'OBSERVATION_LENGTH_LABEL' | translate:{ measurementName: lengthType } }}: <span
        style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
    <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div tooltip="{{'Enter the measurement starting point of cuts'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers style="width:100%">
        <div [formGroup]="lengthForm">
          <mat-form-field style="width:100%">
            <input matInput type="text" [disabled]='helpService.helpMode || readOnly || isStartValid'
              [(ngModel)]="measurementLength" maxlength="18"
              placeholder="{{'OBSERVATION_LENGTH_PLACEHOLDER' | translate}}"
              (keydown)="keyPressed($event, measurementLength, 'length')" step="any" inputmode="decimal"
              formControlName="helpMeasurementLengthCtrl">
          </mat-form-field>
        </div>
      </div>
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
      <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'" tooltip="{{'Tap icon to take measurement length'|translate}}" positionV="bottom"
        [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
        <fa-icon class="icon-style" icon="tape" style="float:right" class="fa-2x" style="color: #666 !important;"
          (click)="(helpService.helpMode || readOnly || !isStartValid) ? test() : takeMeasurement('length')"></fa-icon>
      </div>
    </div>

    <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div style="width:100%">
        <div [formGroup]="lengthForm">
          <mat-form-field style="width:100%">
            <input matInput type="text" [(ngModel)]="measurementLength" maxlength="18"
              placeholder="{{'OBSERVATION_LENGTH_PLACEHOLDER' | translate}}" (change)="onChangeDisable('length')"
              (keydown)="keyPressed($event, measurementLength, 'length')" step="any" inputmode="decimal" [required]
              formControlName="measurementLengthCtrl">
            <mat-error *ngIf="hasErrorStart('length')">{{lengthErrorMessage}}</mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
      <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'">
        <fa-icon class="icon-style" icon="tape" style="float:right" class="fa-2x" style="color: #666 !important;"
          (click)="(helpService.helpMode || readOnly || !isStartValid) ? test() : takeMeasurement('length')"></fa-icon>
      </div>
    </div>
  </div>

  <!-- <div style="width: 100%; text-align: center;">
    <label style="font-weight: bold;margin: 0px">OR</label>
  </div> -->

  <div style="padding:15px 10px 0px 17px" *ngIf="fieldSegment == 'end'">
    <label>{{'OBSERVATION_END_LABEL' | translate}}: <span style="color:rgb(221, 82, 82);font-size:15px;"> *
      </span></label>
    <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div tooltip="{{'Enter the measurement starting point of cuts'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers style="width:100%">
        <div [formGroup]="endForm">
          <mat-form-field style="width:100%">
            <input matInput type="text" [(ngModel)]="measurementEnd" maxlength="18"
              placeholder="{{'OBSERVATION_END_PLACEHOLDER' | translate}}" step="any" inputmode="decimal" [required]
              formControlName="helpMeasurementEndCtrl">
          </mat-form-field>
        </div>
      </div>
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
      <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'" tooltip="{{'Tap icon to take measurement length'|translate}}" positionV="bottom"
        [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
        <fa-icon class="icon-style" icon="tape" style="float:right" class="fa-2x" style="color: #666 !important;"
          (click)="(helpService.helpMode || readOnly || !isStartValid) ? test() : takeMeasurement('end')"></fa-icon>
      </div>
    </div>

    <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div style="width:100%">
        <div [formGroup]="endForm">
          <mat-form-field style="width:100%">
            <input matInput type="text" [(ngModel)]="measurementEnd" maxlength="18"
              placeholder="{{'OBSERVATION_END_PLACEHOLDER' | translate}}" step="any" (change)="onChangeDisable('end')"
              inputmode="decimal" (keydown)="keyPressed($event, measurementEnd, 'end')" [required]
              formControlName="measurementEndCtrl">
            <mat-error *ngIf="hasErrorStart('end')">{{endErrorMessage}}</mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
      <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'">
        <fa-icon class="icon-style" icon="tape" style="float:right" class="fa-2x" style="color: #666 !important;"
          (click)="(helpService.helpMode || readOnly || !isStartValid) ? test() : takeMeasurement('end')"></fa-icon>
      </div>
    </div>
  </div>

  <div style="padding:15px 10px 0px 17px" *ngIf="fieldSegment == 'end'">
    <label>{{'OBSERVATION_LENGTH_LABEL' | translate:{ measurementName: lengthType } }}:</label>
    <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div tooltip="{{'Enter the measurement starting point of cuts'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers style="width:100%">
        <mat-form-field style="width:100%">
          <input matInput id="measLength" type="text" [disabled]='helpService.helpMode || readOnly || isStartValid'
            [(ngModel)]="measurementLength" maxlength="18" placeholder="{{'Length Value' | translate}}"
            (keydown)="keyPressed($event, measurementLength, 'length')" step="any" inputmode="decimal">
        </mat-form-field>
      </div>
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
    </div>

    <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div style="width:100%">
        <mat-form-field style="width:100%">
          <input matInput id="measLength" type="text" [disabled]='true' [(ngModel)]="measurementLength" maxlength="18"
            placeholder="{{'Length Value' | translate}}" (keydown)="keyPressed($event, measurementLength, 'length')"
            step="any" inputmode="decimal">
        </mat-form-field>
      </div>
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
    </div>
  </div>

  <!-- <div style="width: 100%; text-align: center;">
    <label style="font-weight: bold;margin: 0px">OR</label>
  </div> -->

  <div style="padding:15px 10px 0px 17px" *ngIf="fieldSegment == 'length'">
    <label>{{'OBSERVATION_END_LABEL' | translate}}:</label>
    <div *ngIf="helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div tooltip="{{'Enter the measurement starting point of cuts'|translate}}" positionV="bottom"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers style="width:100%">
        <mat-form-field style="width:100%">
          <input matInput type="text" [disabled]='helpService.helpMode || readOnly || isStartValid' [(ngModel)]="ending"
            maxlength="18" placeholder="{{'End Value' | translate}}" (keydown)="keyPressed($event, ending, 'end')"
            step="any" inputmode="decimal">
        </mat-form-field>
      </div>
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
    </div>

    <div *ngIf="!helpService.helpMode" style="display: inline-flex; width: 100%;">
      <div style="width:100%">
        <mat-form-field style="width:100%">
          <input matInput type="text" [disabled]='true' [(ngModel)]="measurementEnd" maxlength="18"
            placeholder="{{'End Value' | translate}}" (keydown)="keyPressed($event, ending, 'end')" step="any"
            inputmode="decimal">
        </mat-form-field>
      </div>
      <div class="form-group" style="padding:10px 10px 0px 17px">
        <label style="padding: 5px; border: solid black 0.5px; border-radius: 5px;">{{dataService.selectedUomString}}
        </label>
      </div>
    </div>
  </div>

  <div style="padding:15px 10px 0px 17px">
    <div>
      <div *ngIf='legOptinsList.length > 1' size="6" style="width: 100%;">
        <label style="font-size:15px;">{{'Observation Location, Leg' | translate}}</label>
      </div>
      <div *ngIf='(helpService.helpMode || readOnly) && legOptinsList.length > 1' size="6"
        style="padding-right:10px; width: 100%;"
        tooltip="{{'Designate a leg number to each leg of the grommet or multi-loop assembly.'|translate}}"
        positionV="bottom" positionH="left" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers>

        <!--<ion-select labelPlacement="stacked"  [disabled]='helpService.helpMode || readOnly' interface="popover"
              [(ngModel)]="selectedLocationOption"
              style="height:44px;border: 1px solid rgb(185, 184, 184);padding-bottom:10px"
              placeholder="{{'Select location option' | translate }}">
              <ion-select-option *ngFor="let option of locationOptionsList" value="{{option}}">{{option}}
              </ion-select-option>
            </ion-select> -->
        <mat-form-field style="width: 100% !important">
          <mat-select disableOptionCentering  [disabled]='true'
            placeholder="{{'Select location option' | translate}}" interface="popover"
            [(value)]="selectedLocationOption">
            <mat-select-trigger *ngIf="selectedLocationOption != undefined && selectedLocationOption != ''">
              {{selectedLocationOption.item}}
              <img width="200" height="70" [src]='selectedLocationOption.img' style="width: auto;">
            </mat-select-trigger>
            <mat-option *ngFor="let option of legOptinsList let i = index" [value]="option"
              style="padding: 10px 5px; height: 100px">
              <img width="200" height="70" [src]='option.img'>
              {{option.item}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div *ngIf='!(helpService.helpMode || readOnly) && locationOptionsList.length > 1' size="6"
        style="padding-right:10px; width: 100%;">
        <!--<ion-select labelPlacement="stacked"  [disabled]='helpService.helpMode || readOnly' interface="popover"
              [(ngModel)]="selectedLocationOption" (ionChange)="observationChanged()"
              style="height:44px;border: 1px solid rgb(185, 184, 184);padding-bottom:10px"
              placeholder="{{'Select location option' | translate }}">
              <ion-select-option *ngFor="let option of locationOptionsList" value="{{option}}">{{option}}
              </ion-select-option>
            </ion-select> -->
        <mat-form-field style="width: 100% !important">
          <mat-select disableOptionCentering 
            placeholder="{{'Select location option' | translate}}" interface="popover"
            [(value)]="selectedLocationOption">
            <mat-select-trigger *ngIf="selectedLocationOption != undefined && selectedLocationOption != ''">
              {{selectedLocationOption.item}}
              <img width="200" height="70" [src]='selectedLocationOption.img' style="width: auto;">
            </mat-select-trigger>
            <mat-option *ngFor="let option of legOptinsList let i = index" [value]="option"
              style="padding: 10px 5px; height: 100px">
              <img width="200" height="70" [src]='option.img'>
              {{option.item}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>
    <div>
      <div *ngIf='layerOptionsList.length > 1' size="6" style="width: 100%;">
        <label style="font-size:15px;">{{'Observation Location, Layer' | translate}}</label>
      </div>
      <div *ngIf='(helpService.helpMode || readOnly) && layerOptionsList.length > 1' size="6"
        style="padding-right:10px; width: 100%;"
        tooltip="{{'Identify which layer of the rope has been affected by the damage.'|translate}}" positionV="bottom"
        positionH="left" [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
        <!--<ion-select labelPlacement="stacked"  [disabled]='helpService.helpMode || readOnly' interface="popover" [(ngModel)]="selectedLayerOption"
          style="height:44px;border: 1px solid rgb(185, 184, 184);padding-bottom:10px"
          placeholder="{{'Select layer option' | translate }}">
          <ion-select-option *ngFor="let option of layerOptionsList" value="{{option}}">{{option}}</ion-select-option>
        </ion-select> -->
        <mat-form-field style="width: 100% !important">
          <mat-select disableOptionCentering [disabled]='true' placeholder="{{'Select layer option' | translate}}"
            interface="popover" [(value)]="selectedLayerOption">
            <mat-select-trigger *ngIf="selectedLayerOption != undefined && selectedLayerOption != ''">
              {{selectedLayerOption.item}}
              <img *ngIf="selectedLayerOption.img !=''" width="70" height="70" [src]='selectedLayerOption.img'
                style="width: auto;">
            </mat-select-trigger>
            <mat-option *ngFor="let option of layerOptionsList let i = index" [value]="option"
              style="padding: 10px 5px; height: 100px">
              <img *ngIf="option.img !=''" width="70" height="70" [src]='option.img'>
              {{option.item}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div *ngIf='!(helpService.helpMode || readOnly) && layerOptionsList.length > 1' size="6"
        style="padding-right:10px; width: 100%;">
        <!--<ion-select labelPlacement="stacked"  [disabled]='helpService.helpMode || readOnly' interface="popover" [(ngModel)]="selectedLayerOption"
          (ionChange)="layerChange($event)" style="height:44px;border: 1px solid rgb(185, 184, 184);padding-bottom:10px"
          placeholder="{{'Select layer option' | translate }}">
          <ion-select-option *ngFor="let option of layerOptionsList" value="{{option}}">{{option}}</ion-select-option>
        </ion-select> -->
        <mat-form-field style="width: 100% !important">
          <mat-select disableOptionCentering (selectionChange)="layerChange($event)"
            placeholder="{{'Select layer option' | translate}}" interface="popover" [(value)]="selectedLayerOption">
            <mat-select-trigger *ngIf="selectedLayerOption != undefined && selectedLayerOption != ''">
              {{selectedLayerOption.item}}
              <img *ngIf="selectedLayerOption.img !=''" width="70" height="70" [src]='selectedLayerOption.img'
                style="width: auto;">
            </mat-select-trigger>
            <mat-option *ngFor="let option of layerOptionsList let i = index" [value]="option"
              style="padding: 10px 5px; height: 100px">
              <img *ngIf="option.img !=''" width="70" height="70" [src]='option.img'>
              {{option.item}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>
  </div>


  <div *ngIf="product != 'AMSTEEL-BLUE' && product !='K ™ 100' && product != 'TENEX'" style="margin-right:10px;">
    <div *ngIf='helpService.helpMode' style="padding:0px 10px 0px 17px" class="form-group"
      tooltip="{{'Select the severity level'|translate}}" positionV="bottom" positionH="right"
      [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
      [hideOthers]=helpService.hideOthers>
      <ion-label style="font-size:15px;padding-bottom:5px;">{{'Select the Severity Level' | translate}}: <span
          style="color:rgb(221, 82, 82);font-size:15px;"> * </span></ion-label>
     <ion-select labelPlacement="stacked"  interface="popover" [(ngModel)]="damageType" [disabled]='helpService.helpMode || readOnly'
        style="height:44px;padding:0px 10px;border-radius: 5px;border:1px solid rgb(153, 151, 151);margin-top:10px;"
        placeholder="{{'Select severity type' | translate}}" (ionChange)="setChanged()">
        <ion-select-option value="none">{{'None' | translate}}</ion-select-option>
        <ion-select-option value="mild">{{'Mild' | translate}}</ion-select-option>
        <ion-select-option value="moderate">{{'Moderate' | translate}}</ion-select-option>
        <ion-select-option value="severe">{{'Severe' | translate}}</ion-select-option>
      </ion-select>
    </div>

    <div *ngIf='!helpService.helpMode' style="padding:0px 10px 0px 17px" class="form-group">
      <ion-label style="font-size:15px;padding-bottom:5px;">{{'Select the Severity Level' | translate}}: <span
          style="color:rgb(221, 82, 82);font-size:15px;"> * </span></ion-label>
     <ion-select labelPlacement="stacked"  interface="popover" [(ngModel)]="damageType" [disabled]='helpService.helpMode || readOnly'
        style="height:44px;padding:0px 10px;border-radius: 5px;border:1px solid rgb(153, 151, 151);margin-top:10px;"
        placeholder="{{'Select severity type' | translate}}" (ionChange)="setChanged()">
        <ion-select-option value="none">{{'None' | translate}}</ion-select-option>
        <ion-select-option value="mild">{{'Mild' | translate}}</ion-select-option>
        <ion-select-option value="moderate">{{'Moderate' | translate}}</ion-select-option>
        <ion-select-option value="severe">{{'Severe' | translate}}</ion-select-option>
      </ion-select>
    </div>
  </div>
  <ion-grid style="padding:15px 10px 0px 17px" *ngIf="showRiskRating  && !(inspectionHeader.MANUFACTURER != 'SAMSON' && inspectionHeader.MANUFACTURER != 'Samson' && inspectionHeader.MANUFACTURER != 'samson')">
    <ion-row style="padding: 0px 10px 0px 17px;" *ngIf="showRiskRating  && !(inspectionHeader.MANUFACTURER != 'SAMSON' && inspectionHeader.MANUFACTURER != 'Samson' && inspectionHeader.MANUFACTURER != 'samson')">
      <ion-col *ngIf="damageType != null && damageType != undefined && damageType != '' && damageType != 'none'" style="display: inline-flex;">
        <fa-icon *ngIf="damageType == 'mild'" icon="circle-check" style="font-size:25px;color: green; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <fa-icon *ngIf="damageType == 'moderate'" icon="circle-check" style="font-size:25px;color: orange; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <fa-icon *ngIf="damageType == 'severe'" icon="circle-exclamation" style="font-size:25px;color: red; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <p *ngIf="damageType == 'mild'" style="line-height: 35px; margin-bottom: 0px">{{'No action needed' | translate}}</p>
        <p *ngIf="damageType == 'moderate'" style="line-height: 35px; margin-bottom: 0px">{{'Contact manufacturer for guidance' | translate}}</p>
        <p *ngIf="damageType == 'severe'" style="line-height: 35px; margin-bottom: 0px">{{'Retire end in use' | translate}}</p>
      </ion-col> 
    </ion-row>
  </ion-grid>

  <ion-grid style="padding-left:17px;" *ngIf="dataService.isUserEnabledForInsightAI && inspectionHeader.MANUFACTURER=='Samson' && inspectionHeader.CONSTRUCTION=='12-Strand' && (['HMSF (Class II)','HMPE (Class II)'].includes(inspectionHeader.PRODUCT_TYPE) || inspectionHeader.PRODUCT_TYPE==('Conventional Fiber (Class I)')) && (product == 'AMSTEEL-BLUE' || product =='K ™ 100' || product == 'TENEX')">
    <ion-label>Please select the type of inspection you want to conduct?</ion-label>
      <ion-row>
        <ion-col>
          <ion-item>
            <ion-checkbox labelPlacement="end" justify="start" mode="ios" value='VisionAI' (ionChange)="visionTypeChanged($event)" [disabled]='helpService.helpMode || readOnly' [checked]="inspectionType=='VisionAI'">Insight AI</ion-checkbox>
          </ion-item>
        </ion-col>
        <ion-col>
          <ion-item>
            <ion-checkbox labelPlacement="end" justify="start" mode="ios" value="Manual" (ionChange)="visionTypeChanged($event)" [disabled]='helpService.helpMode || readOnly' [checked]="inspectionType=='Manual'">Manual</ion-checkbox>
          </ion-item>
        </ion-col>
      </ion-row>
  </ion-grid>

  <div *ngIf="dataService.isUserEnabledForInsightAI && inspectionType=='VisionAI' && inspectionHeader.MANUFACTURER=='Samson' && inspectionHeader.CONSTRUCTION=='12-Strand' && (['HMSF (Class II)','HMPE (Class II)'].includes(inspectionHeader.PRODUCT_TYPE) || inspectionHeader.PRODUCT_TYPE==('Conventional Fiber (Class I)')) && (product == 'AMSTEEL-BLUE' || product =='K ™ 100' || product == 'TENEX')" class="visionAI">
    <label class="visionLabel" style="padding-left:17px;">Icaria:</label>
    <div class="visionDiv">
      <ion-fab-button color="primary" size="small" (click)="openCamera()" [disabled]="visionButtonDisabled || helpService.helpMode || readOnly">
        <ion-icon name="camera"></ion-icon>
      </ion-fab-button>
      <ion-button color="primary" style="margin-top:10px;margin-right:5px;" [disabled]="" *ngIf="externalAIRange" (click)="previewInsightAIResult()">Results</ion-button>
    </div>
    <label style="padding-left:17px;" *ngIf="externalAIRange>=0 && visionButtonDisabled" >The predicted score is : {{ externalAIRange }} </label>
  </div>

  <div class="form-group" *ngIf="product == 'AMSTEEL-BLUE' || product =='K ™ 100' || product == 'TENEX'" style="margin-bottom: 0px;">
    <label style="padding-left:17px;">{{'EXTERNAL_ABRATION_RATING' | translate}}<span
        style="color:rgb(221, 82, 82);font-size:15px;">
        * </span> :</label>
    <ion-row>
      <ion-col *ngIf='helpService.helpMode' tooltip="{{'Set the rating of the external abrasion' | translate}}"
        positionV="bottom" [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent"
        [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
        <ion-range *ngif="inspectionType=='Manual'" [disabled]='helpService.helpMode || readOnly' [(ngModel)]="externalRange" min="0" max={{maxRating}}
          color="secondary" pin="false" mode="ios" (ionChange)="setChangedRating()">
          <ion-label slot="start">{{'0' | translate}}</ion-label>
          <ion-label slot="end">{{maxRating}}</ion-label>
          <ion-label slot="end">
            <ion-chip outline="true" color="primary" style="height:25px;"> {{externalRange}}
            </ion-chip>
          </ion-label>
        </ion-range>
        <ion-range *ngif="inspectionType=='VisionAI'" [disabled]='helpService.helpMode || readOnly || inspectionType=="VisionAI"' [(ngModel)]="externalAIRange" min="0" max={{maxRating}}
          color="secondary" pin="false" mode="ios">
          <ion-label slot="start">{{'0' | translate}}</ion-label>
          <ion-label slot="end">{{maxRating}}</ion-label>
          <ion-label slot="end">
            <ion-chip outline="true" color="primary" style="height:25px;"> {{externalAIRange}}
            </ion-chip>
          </ion-label>
        </ion-range>
      </ion-col>

      <ion-col *ngIf='!helpService.helpMode'>
        <ion-range *ngIf="inspectionType=='Manual'" [disabled]='helpService.helpMode || readOnly' [(ngModel)]="externalRange" min="0" max={{maxRating}}
          color="secondary" pin="false" mode="ios" (ionChange)="setChangedRating()">
          <ion-label slot="start">{{'0' | translate}}</ion-label>
          <ion-label slot="end">{{maxRating}}</ion-label>
          <ion-label slot="end">
            <ion-chip outline="true" color="primary" style="height:25px;"> {{externalRange}}
            </ion-chip>
          </ion-label>
        </ion-range>
        <ion-range *ngIf="inspectionType=='VisionAI'" [disabled]='helpService.helpMode || readOnly || inspectionType=="VisionAI"' [(ngModel)]="externalAIRange" min="0" max={{maxRating}}
          color="secondary" pin="false" mode="ios" >
          <ion-label slot="start">{{'0' | translate}}</ion-label>
          <ion-label slot="end">{{maxRating}}</ion-label>
          <ion-label slot="end">
            <ion-chip outline="true" color="primary" style="height:25px;"> {{externalAIRange}}
            </ion-chip>
          </ion-label>
        </ion-range>
      </ion-col>
    </ion-row>
    <span style="color:rgb(221, 82, 82);padding-left:17px;margin-top:0px;" *ngIf="ratingValidation">{{'Please set therating' | translate}}</span>
  </div>

  <!-- & for inspection type Insight AI start -->
  <ion-grid style="padding:15px 10px 0px 17px" *ngIf="inspectionType=='VisionAI' && showRiskRating && (selectedProduct == 'AMSTEEL-BLUE' || selectedProduct == 'SATURN-12') && (inspectionHeader.MANUFACTURER == 'SAMSON' && inspectionHeader.MANUFACTURER == 'Samson' && inspectionHeader.MANUFACTURER == 'samson')">
    <ion-row style="padding: 0px 10px 0px 17px;" *ngIf="showRiskRating  && !(inspectionHeader.MANUFACTURER != 'SAMSON' && inspectionHeader.MANUFACTURER != 'Samson' && inspectionHeader.MANUFACTURER != 'samson')">
      <ion-col *ngIf="externalAIRange > 0" style="display: inline-flex;">
        <fa-icon *ngIf="externalAIRange <= 2" icon="circle-check" style="font-size:25px;color: green; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <fa-icon *ngIf="externalAIRange > 2 && externalAIRange <= 5" icon="circle-check" style="font-size:25px;color: orange; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <fa-icon *ngIf="externalAIRange > 5" icon="circle-exclamation" style="font-size:25px;color: red; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <p *ngIf="externalAIRange <= 2" style="line-height: 35px; margin-bottom: 0px">{{'No action needed' | translate}}</p>
        <p *ngIf="externalAIRange > 2 && externalAIRange <= 5" style="line-height: 35px; margin-bottom: 0px">{{'Contact manufacturer for guidance' | translate}}</p>
        <p *ngIf="externalAIRange > 5" style="line-height: 35px; margin-bottom: 0px">{{'Retire end in use' | translate}}</p>
      </ion-col>
    </ion-row>
  </ion-grid>
  <!-- & for inspection type Insight AI end -->

  <!-- & for inspection type Manual start-->
   <ion-grid style="padding:15px 10px 0px 17px" *ngIf="inspectionType=='Manual' && showRiskRating && (selectedProduct == 'AMSTEEL-BLUE' || selectedProduct == 'SATURN-12') && !(inspectionHeader.MANUFACTURER != 'SAMSON' && inspectionHeader.MANUFACTURER != 'Samson' && inspectionHeader.MANUFACTURER != 'samson')">
    <ion-row style="padding: 0px 10px 0px 17px;" *ngIf="showRiskRating  && !(inspectionHeader.MANUFACTURER != 'SAMSON' && inspectionHeader.MANUFACTURER != 'Samson' && inspectionHeader.MANUFACTURER != 'samson')">
      <ion-col *ngIf="externalRange > 0" style="display: inline-flex;">
        <fa-icon *ngIf="externalRange <= 2 " icon="circle-check" style="font-size:25px;color: green; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <fa-icon *ngIf="(externalRange > 2 && externalRange <= 5)" icon="circle-check" style="font-size:25px;color: orange; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <fa-icon *ngIf="externalRange > 5" icon="circle-exclamation" style="font-size:25px;color: red; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <p *ngIf="externalRange <= 2" style="line-height: 35px; margin-bottom: 0px">{{'No action needed' | translate}}</p>
        <p *ngIf="(externalRange > 2 && externalRange <= 5)" style="line-height: 35px; margin-bottom: 0px">{{'Contact manufacturer for guidance' | translate}}</p>
        <p *ngIf="externalRange > 5" style="line-height: 35px; margin-bottom: 0px">{{'Retire end in use' | translate}}</p>
      </ion-col>
    </ion-row>
  </ion-grid>
  <!-- & for inspection type Manual end-->

  <ion-grid style="padding:15px 10px 0px 17px" *ngIf="inspectionType=='Manual' && showRiskRating && (selectedProduct != 'AMSTEEL-BLUE' && selectedProduct != 'SATURN-12' && selectedProduct != 'TENEX') && (inspectionHeader.MANUFACTURER == 'SAMSON' && inspectionHeader.MANUFACTURER == 'Samson' && inspectionHeader.MANUFACTURER == 'samson')">
    <ion-row style="padding: 0px 10px 0px 17px;" *ngIf="showRiskRating  && (inspectionHeader.MANUFACTURER == 'SAMSON' && inspectionHeader.MANUFACTURER == 'Samson' && inspectionHeader.MANUFACTURER == 'samson')">
      <ion-col *ngIf="externalRange > 0" style="display: inline-flex;">
        <fa-icon *ngIf="externalRange <= 2" icon="circle-check" style="font-size:25px;color: green; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <fa-icon *ngIf="externalRange > 2 && externalRange <= 5" icon="circle-check" style="font-size:25px;color: orange; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <fa-icon *ngIf="externalRange > 5" icon="circle-exclamation" style="font-size:25px;color: red; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <p *ngIf="externalRange <= 2" style="line-height: 35px; margin-bottom: 0px">{{'No action needed' | translate}}</p>
        <p *ngIf="externalRange > 2 && externalRange <= 5" style="line-height: 35px; margin-bottom: 0px">{{'Contact manufacturer for guidance' | translate}}</p>
        <p *ngIf="externalRange > 5" style="line-height: 35px; margin-bottom: 0px">{{'Retire end in use' | translate}}</p>
      </ion-col>
    </ion-row>
  </ion-grid>

  <ion-grid style="padding:15px 10px 0px 17px" *ngIf="showRiskRating && selectedProduct == 'TENEX' && !(inspectionHeader.MANUFACTURER != 'SAMSON' && inspectionHeader.MANUFACTURER != 'Samson' && inspectionHeader.MANUFACTURER != 'samson')">
    <ion-row style="padding: 0px 10px 0px 17px;" *ngIf="showRiskRating  && !(inspectionHeader.MANUFACTURER != 'SAMSON' && inspectionHeader.MANUFACTURER != 'Samson' && inspectionHeader.MANUFACTURER != 'samson')">
      <ion-col *ngIf="externalRange > 0" style="display: inline-flex;">
        <fa-icon *ngIf="externalRange <= 2" icon="circle-check" style="font-size:25px;color: green; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <fa-icon *ngIf="externalRange > 2 && externalRange <= 3" icon="circle-check" style="font-size:25px;color: orange; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <fa-icon *ngIf="externalRange > 3" icon="circle-exclamation" style="font-size:25px;color: red; margin-left: 0px !important; margin-right: 15px !important"></fa-icon>
        <p *ngIf="externalRange <= 2" style="line-height: 35px; margin-bottom: 0px">{{'No action needed' | translate}}</p>
        <p *ngIf="externalRange > 2 && externalRange <= 3" style="line-height: 35px; margin-bottom: 0px">{{'Contact manufacturer for guidance' | translate}}</p>
        <p *ngIf="externalRange > 3" style="line-height: 35px; margin-bottom: 0px">{{'Retire end in use' | translate}}</p>
      </ion-col>
    </ion-row>
  </ion-grid>



  <!-- & Support Status section -->
  <ion-grid style="padding:15px 10px 0px 17px" *ngIf="inspectionHeader.MANUFACTURER != 'SAMSON' && inspectionHeader.MANUFACTURER != 'Samson' && inspectionHeader.MANUFACTURER != 'samson' ">
    <label style="padding-left:5px;">{{'Pass or Fail' | translate}}: <span style="color:rgb(221, 82, 82);font-size:15px;"> * </span></label>
    <ion-radio-group [(ngModel)]="supportStatus" *ngIf="helpService.helpMode">
      <ion-row tooltip="{{'Select if the line passes or fails the specific manufacturers inspection criteria for safe and continued use.'|translate}}" positionV="bottom" positionH="right"
        [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent" [duration]="helpService.duration"
        [hideOthers]=helpService.hideOthers>
        <ion-col>
          <ion-item class='ion-radio-item-style'>
            <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="pass" [disabled]='helpService.helpMode || readOnly'>{{'Pass' | translate}}
            </ion-radio>
            <!-- <ion-label>{{'Pass' | translate}}</ion-label> -->
          </ion-item>
        </ion-col>
        <ion-col>
          <ion-item class='ion-radio-item-style'>
            &nbsp;&nbsp;
            <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="fail" [disabled]='helpService.helpMode || readOnly'>{{'Fail' | translate}}</ion-radio>
            <!-- <ion-label>{{'Fail' | translate}}</ion-label> -->
          </ion-item>
        </ion-col>
      </ion-row>
    </ion-radio-group>

    <ion-radio-group [(ngModel)]="supportStatus" *ngIf="!helpService.helpMode">
      <ion-row>
        <ion-col>
          <ion-item class='ion-radio-item-style'>
            <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="pass" [disabled]='helpService.helpMode || readOnly'>{{'Pass' | translate}}
            </ion-radio>
            <!-- <ion-label>{{'Pass' | translate}}</ion-label> -->
          </ion-item>
        </ion-col>
        <ion-col>
          <ion-item class='ion-radio-item-style'>
            &nbsp;&nbsp;
            <ion-radio  labelPlacement="end" justify="start" mode="md" item-left value="fail" [disabled]='helpService.helpMode || readOnly'>{{'Fail' | translate}}</ion-radio>
            <!-- <ion-label>{{'Fail' | translate}}</ion-label> -->
          </ion-item>
        </ion-col>
      </ion-row>
    </ion-radio-group>
  </ion-grid>

  <!-- Observation Notes Section -->
  <div style="padding:15px 10px 10px 17px">
    <label>{{'OBSERVATION_NOTES' | translate}}:</label>
    <ion-textarea auto-grow='true' style="border: 0.5px solid black; border-radius: 5px; padding-left: 8px;"
      [disabled]='helpService.helpMode || readOnly' [(ngModel)]="observationNote" (ionFocus)="onFocusUserInputField($event)"></ion-textarea>
  </div>

  <ion-row style="padding: 10px 17px 10px 17px;" *ngIf="device.platform != 'browser'">
    <div class="img-wrap" *ngFor="let item of cameraService.editedImg; let i = index"
      style="width:50%;height:200px;padding:2px 2px 2px 2px;border:1px solid grey;border-radius: 5px;">
      <img [src]='item.Image' style="float:left;width:100%;height:100%;object-fit:contain;border-radius: 5px;"
        (click)="(helpService.helpMode || readOnly) ? test() : saveAndEdit(item,i)" />
      <ion-icon *ngIf="item!='./assets/img/samson2.png' && (inspectionType=='VisionAI' ? checkAIImgLatest(i)==true : true)" name="trash" class="close" slot="end" mode="md" color="danger"
        (click)="(helpService.helpMode || readOnly) ? test() : deleteImage(i)"></ion-icon>
    </div>
    <div *ngIf='helpService.helpMode' [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:50%;height:200px;padding:2px 2px 2px 2px;border-radius: 5px;">
      <img src="./assets/img/addImage.png" tooltip="{{'Tap to take a photo of rope external condition'|translate}}"
        positionV="bottom" [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent"
        [duration]="helpService.duration" [hideOthers]=helpService.hideOthers
        (click)="(helpService.helpMode || readOnly) ? test() : '' "
        style="object-fit:contain;border-radius: 5px;padding-top:52px" />
    </div>

    <div *ngIf='!helpService.helpMode' [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:50%;height:200px;padding:2px 2px 2px 2px;border-radius: 5px;">
      <img src="./assets/img/addImage.png"
        (click)="(helpService.helpMode || readOnly) ? test() : saveAndCapture() "
        style="object-fit:contain;border-radius: 5px;padding-top:52px" />
    </div>
  </ion-row>

  <ion-row style="padding: 10px 17px 10px 17px;" *ngIf="device.platform == 'browser'">
    <input id="myInput" type="file" style="visibility:hidden; display: none;" accept="image/x-png,image/jpeg" (change)="cameraService.onFileSelected($event)"/>
    <div class="img-wrap" *ngFor="let item of cameraService.editedImg; let i = index"
      style="width:50%;height:200px;padding:2px 2px 2px 2px;border:1px solid grey;border-radius: 5px;">
      <img [src]='item.Image' style="float:left;width:100%;height:100%;object-fit:contain;border-radius: 5px;"
      (click)="(helpService.helpMode || readOnly) ? test() : item.mode!='insightAI'?saveOnPause():''; item.mode!='insightAI'?cameraService.editImage(item.Image, i, 'external','', pageComponent, maxRating, externalRange, selectedProduct):''; setChanged()" />
      <ion-icon *ngIf="item.Image!='./assets/img/samson2.png' && (inspectionType=='VisionAI' ? checkAIImgLatest(i)==true : true)" name="trash" class="close" slot="end" mode="md" color="danger"
      (click)="(helpService.helpMode || readOnly) ? test() : deleteImage(i)" ></ion-icon>
    </div>
    <div *ngIf='helpService.helpMode' [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:50%;height:200px;padding:2px 2px 2px 2px;border-radius: 5px;">
      <img src="./assets/img/addImage.png" tooltip="{{'Tap to take a photo of rope external condition'|translate}}"
        positionV="bottom" [arrow]="helpService.showArrow" [event]="helpService.tooltipEvent"
        [duration]="helpService.duration" [hideOthers]=helpService.hideOthers
        (click)="(helpService.helpMode || readOnly) ? test() : cameraStarted = true; cameraService.takePicture('','external', pageComponent, maxRating, externalRange); setChanged()"
        style="object-fit:contain;border-radius: 5px;padding-top:52px" />
    </div>

    <div *ngIf='!helpService.helpMode' [ngClass]="imageEmpty ? 'imageError' :'imageEmpty'"
      style="text-align:center;width:50%;height:200px;padding:2px 2px 2px 2px;border-radius: 5px;">
      <img src="./assets/img/addImage.png"
        (click)="(helpService.helpMode || readOnly) ? test() : cameraStarted = true; cameraService.takePicture('','external', pageComponent, maxRating, externalRange, selectedProduct); setChanged()"
        style="object-fit:contain;border-radius: 5px;padding-top:52px" />
    </div>
  </ion-row>
  <ion-fab *ngIf='!readOnly' vertical="bottom" horizontal="end" slot="fixed"
    tooltip="{{'Tap to save external abrasion measurement'|translate}}" positionV="top" positionH="right"
    [topOffset]=helpService.topOffset [event]="helpService.tooltipEvent" [duration]="helpService.duration" [hideOthers]=helpService.hideOthers>
    <ion-fab-button color="primary" (click)="(helpService.helpMode || readOnly) ? test() : saveMeasurement()">
      <fa-icon class="icon-style-other" icon="floppy-disk" style="font-size:22px;"></fa-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>
<div *ngIf='footerClose'>
  <div class="help-block" (click)="helpService.switchMode(); disableFormFields();" *ngIf="helpService.helpMode"
    style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
    <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
  </div>
  <ion-footer class="footer-style" *ngIf='!isEmpEditConfig'>
    <div class="help-block" (click)="helpService.switchMode(); disableFormFields();" *ngIf="helpService.helpMode"
      style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
      <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
    </div>
    <ion-grid style="text-align:center;">
      <ion-row>
        <ion-col>
          <div (click)="openMenu()">
            <fa-icon class="icon-style" icon="bars"></fa-icon>
          </div>
        </ion-col>
        <ion-col>
          <div (click)="goToLineTracker()" style=" outline-color: rgba(0, 0, 0, 0);" class="ion-activatable">
            <ion-ripple-effect></ion-ripple-effect>
            <fa-icon class="icon-style" icon="list-check"></fa-icon>
          </div>
        </ion-col>
        <!-- <ion-col>
          <div style=" outline-color: rgba(0, 0, 0, 0);" class="ion-activatable" (click)="gotoHome()">
            <ion-ripple-effect></ion-ripple-effect>
            <fa-icon class="icon-style" icon="home"></fa-icon>
          </div>
        </ion-col> -->
        <ion-col>
          <div style=" outline-color: rgba(0, 0, 0, 0);" class="ion-activatable" (click)="gotoInspections()">
            <ion-ripple-effect></ion-ripple-effect>
            <!-- <fa-icon class="icon-style" icon="search"></fa-icon> -->
            <img src="./assets/icon/Rope_Inspection_ICON_3A.png" class="bottom-bar-image-style fa-fw">
          </div>
        </ion-col>
        <ion-col>
          <div style=" outline-color: rgba(0, 0, 0, 0);" class="ion-activatable" (click)="gotoResources()">
            <ion-ripple-effect></ion-ripple-effect>
            <fa-icon class="icon-style" icon="grip"></fa-icon>
          </div>
        </ion-col>
        <ion-col>
          <div style=" outline-color: rgba(0, 0, 0, 0);" class="ion-activatable" (click)="gotoContact()">
            <ion-ripple-effect></ion-ripple-effect>
            <fa-icon class="icon-style" icon="envelope" class="icon-style"></fa-icon>
          </div>
        </ion-col>
      </ion-row>
    </ion-grid>
  </ion-footer>
</div>