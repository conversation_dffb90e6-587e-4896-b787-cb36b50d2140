.ion-radio-item-style {
    --padding-start: 0px !important;
    --inner-border-width: 0px !important;

    ion-label {
        padding-left: 10px !important;
        padding-top: 2px;
    }
}

.ion-radio-row {
    width: 200px !important
}

.img-wrap {
    position: relative;
    display: inline-block;
    width: 99%
}

.img-wrap .close {
    position: absolute;
    top: 10px;
    right: 15px;
    text-align: center;
    font-size: 26px;
    line-height: 11px;
    border-radius: 50%;
    opacity: 1;
}

.right-div {
    display: inline-block;
    min-height: 20px;
    width: 60%;
    margin: 5px 0px;
    padding: 0px 0px;
    font-size: 12px !important;
}

.left-div {
    float: left;
    margin: 5px 0px;
    padding-left: 15px !important;
    width: 40%;
    font-size: 14px !important;
    color: black !important;
    // padding: 0px 7px;      
}

.imageEmpty {
    border: 1px solid grey;
}

.imageError {
    border: 1px solid rgb(221, 82, 82);
}

.popover-content {
    width: auto !important;
}