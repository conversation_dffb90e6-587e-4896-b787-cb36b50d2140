import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { DocumentViewer } from '@awesome-cordova-plugins/document-viewer/ngx';
import { FileOpener } from '@awesome-cordova-plugins/file-opener/ngx';
import { HTTP } from '@awesome-cordova-plugins/http/ngx';
import { Network } from '@awesome-cordova-plugins/network/ngx';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { Zip } from '@awesome-cordova-plugins/zip/ngx';
import { IonSelect, MenuController, NavController, AlertController, Platform, ModalController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { AlertService } from 'src/app/services/alert.service';
import { DataService } from 'src/app/services/data.service';
import { HelpService } from 'src/app/services/help.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';

import { File } from '@awesome-cordova-plugins/file/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faTh } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-guest-resource',
  templateUrl: './guest-resource.page.html',
  styleUrls: ['./guest-resource.page.scss'],
})
export class GuestResourcePage implements OnInit {

  selection: any;
  hideList = true;

  customizationAlert: any;

  @ViewChild('countryList') countrySelectRef: IonSelect;
  

  constructor(private router: Router,
    public translate: TranslateService,
    public dataService: DataService,
    private menu: MenuController,
    public navCtrl: NavController,
    public alertController: AlertController,
    public helpService: HelpService,
    public plt: Platform,
    public document: DocumentViewer,
    public file: File,
    public unviredSdk: UnviredCordovaSDK,
    public network: Network,
    public modalController: ModalController,
    public alertService: AlertService,
    public fileOpener: FileOpener,
    public device: Device,
    private nativeHTTP: HTTP,
    private zip: Zip , 
    private faIconLibrary: FaIconLibrary) {
      this.faIconLibrary.addIcons(faBars , faTh)
     }

  ngOnInit() {
    if(this.device.platform == "Android") { 
      this.checkAndDownloadResources()
    }
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  ionViewDidLeave() {
    this.helpService.exitHelpMode();
  }
  back() {
    this.router.navigate(['guest-home']);
  }

  openAbrasion() {
    this.router.navigate(['guest-resource-abrasion'])
  }

  openInspections() {
    this.router.navigate(['guest-resource-inspections'])
  }

  openPdfExternal() {
    let filePath = this.file.applicationDirectory + 'www/assets';
    // if(this.plt.is("android")) {
    let fakeName = Date.now();
    this.file.copyFile(filePath, 'Abrasion-External.pdf', this.file.dataDirectory, `Abrasion-External.pdf`).then(result => {
      this.fileOpener.open(result.nativeURL, 'application/pdf')
    }, error => {
      if (error.code == 12) {
        this.fileOpener.open(this.file.dataDirectory + `/Abrasion-External.pdf`, 'application/pdf')
      }
      console.log(JSON.stringify(error))
    });
    // } else {
    //   const options: DocumentViewerOptions = {
    //     title: 'External'
    //   }
    //   this.document.viewDocument(`${filePath}/Abrasion-External.pdf`, 'application/pdf', options)
    // }
  }

  openPdfInternal() {
    let filePath = this.file.applicationDirectory + 'www/assets';
    // if(this.plt.is("android")) {
    let fakeName = Date.now();
    this.file.copyFile(filePath, 'Abrasion-Internal.pdf', this.file.dataDirectory, `Abrasion-Internal.pdf`).then(result => {
      this.fileOpener.open(result.nativeURL, 'application/pdf')
    }, error => {
      if (error.code == 12) {
        this.fileOpener.open(this.file.dataDirectory + `/Abrasion-Internal.pdf`, 'application/pdf')
      }
      console.log(JSON.stringify(error))
    });
    // } else {
    //   const options: DocumentViewerOptions = {
    //     title: 'Internal'
    //   }
    //   this.document.viewDocument(`${filePath}/Abrasion-Internal.pdf`, 'application/pdf', options)
    // }
  }

  openPdfSinglebraid() {
    let filePath = this.file.applicationDirectory + 'www/assets';
    // if(this.plt.is("android")) {
    let fakeName = Date.now();
    this.file.copyFile(filePath, 'App_SB_Inspection_Checklist_2017.pdf', this.file.dataDirectory, `App_SB_Inspection_Checklist_2017.pdf`).then(result => {
      this.fileOpener.open(result.nativeURL, 'application/pdf')
    }, error => {
      if (error.code == 12) {
        this.fileOpener.open(this.file.dataDirectory + `/App_SB_Inspection_Checklist_2017.pdf`, 'application/pdf')
      }
      console.log(JSON.stringify(error))
    });
    // } else {
    //   const options: DocumentViewerOptions = {
    //     title: 'Single braid'
    //   }
    //   this.document.viewDocument(`${filePath}/App_SB_Inspection_Checklist_2017.pdf`, 'application/pdf', options)
    // }
  }

  openPdfDoublebraid() {
    let filePath = this.file.applicationDirectory + 'www/assets';
    // if(this.plt.is("android")) {
    let fakeName = Date.now();
    this.file.copyFile(filePath, 'App_DB_Inspection_Checklist_2017.pdf', this.file.dataDirectory, `App_DB_Inspection_Checklist_2017.pdf`).then(result => {
      this.fileOpener.open(result.nativeURL, 'application/pdf')
    }, error => {
      if (error.code == 12) {
        this.fileOpener.open(this.file.dataDirectory + `/App_DB_Inspection_Checklist_2017.pdf`, 'application/pdf')
      }
      console.log(JSON.stringify(error))
    });
    // this.file.copyFile(filePath, 'App_DB_Inspection_Checklist_2017.pdf', this.file.dataDirectory, `${fakeName}.pdf`).then(result => {
    //   this.fileOpener.open(result.nativeURL, 'application/pdf')
    // });      
    // } else {
    //   const options: DocumentViewerOptions = {
    //     title: 'Double braid',
    //     documentView: {
    //       closeLabel: "test"
    //     }
    //   }
    //   this.document.viewDocument(`${filePath}/App_DB_Inspection_Checklist_2017.pdf`, 'application/pdf', options)
    // }
  }

  gotoGuestHome() {
      this.router.navigate(['guest-home']);
  }

  async gotoGuestInspections() {     
    const alert = await this.alertController.create({
      message: this.translate.instant("GUEST_MESSAGE"),
      buttons: ['OK']
    });
    await alert.present();
  }

  gotoGuestResources() {
      this.router.navigate(['guest-resource']);
  }

  gotoGuestContact() {
      this.router.navigate(['guest-contact']);
  }

  openSpliceInstructions() {
    this.router.navigate(['guest-resources-splice-instruction']);
  }

  checkAndDownloadResources() {
    this.file.checkDir(this.file.dataDirectory, "pdf").then(() => {
      console.log("Already Downloaded")
      }).catch((err) => {
        this.showResourceDownloadAlert(true)
        console.log("Download resources")
      });

  }

  async showResourceDownloadAlert(closePage) {
    const alert = await this.alertController.create({
      header: 'Alert',
      message: 'Do you want download resource files?',
      buttons: [
        {
          text: 'Later',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {
            if(closePage == true) {
              this.router.navigateByUrl('/guest-home')
            }
          }
        }, {
          text: 'Yes',
          handler: async () => {
            this.downloadResources();
            // this.route.navigate(['user-preferences']);
          }
        }
      ],
      backdropDismiss: false
    });
    await alert.present();
  }

  async downloadResources() {
    this.showResourceDownloadLoading();
    try {
      var url = "https://sandbox.unvired.io/samson/resources.zip";
      const tempUser = await this.unviredSdk.userSettings();
      
      if (tempUser && tempUser.data && tempUser.data["SERVER_URL"]) {
        const tempzSettings = tempUser.data["SERVER_URL"];
        
        if (tempzSettings.indexOf('/UMP/') > 0) {
          url = tempzSettings.replace('/UMP/','/samson/resources.zip');
        } else if (tempzSettings.indexOf('/UMP') > 0) {
          url = tempzSettings.replace('/UMP', '/samson/resources.zip');
        }
      } else {
        console.log("SERVER_URL not found in user settings, using default URL");
        this.unviredSdk.logInfo("Resources", "downloadResources", "SERVER_URL not found, using default URL");
      }
      
      this.unviredSdk.logInfo("Resources", "downloadResources", 'Download URL: ' + url);
      this.unviredSdk.logInfo("Resources", "downloadResources", 'Target path: ' + this.file.dataDirectory);
      
      const filePath = this.file.dataDirectory + 'dummy.zip';
      
      this.nativeHTTP.downloadFile(url, {}, {}, filePath).then((entry) => {
        this.unviredSdk.logInfo("home", "downloadResources", 'download complete: ' + entry.toURL());
        
        this.file.checkFile(this.file.dataDirectory, "dummy.zip")
          .then(() => {
            
            this.zip.unzip(this.file.dataDirectory + 'dummy.zip', this.file.dataDirectory, (progress) => {
                // Check if progress is a number or an object with loaded/total properties
                if (typeof progress === 'number') {
                  // Some versions of the plugin return a percentage directly
                  this.unviredSdk.logInfo("home", "downloadResources", 'Unzipping, ' + Math.round(progress) + '%');
                } else if (progress && typeof progress === 'object') {
                  // Check if loaded and total are valid numbers
                  if (typeof progress.loaded === 'number' && typeof progress.total === 'number' && progress.total > 0) {
                    this.unviredSdk.logInfo("home", "downloadResources", 
                      'Unzipping, ' + Math.round((progress.loaded / progress.total) * 100) + '%');
                  } else {
                    // If we can't calculate a percentage, just log that unzipping is in progress
                    this.unviredSdk.logInfo("home", "downloadResources", 'Unzipping in progress...');
                  }
                } else {
                  // Fallback for any other case
                  this.unviredSdk.logInfo("home", "downloadResources", 'Unzipping in progress...');
                }
              })
              .then(async (result) => {
                // Always dismiss the alert when unzipping is done
                if (this.customizationAlert) {
                  this.customizationAlert.dismiss();
                }
                
                // Check if pdf directory exists regardless of unzip result
                try {
                  await this.file.checkDir(this.file.dataDirectory, "pdf");
                  // If we can access the pdf directory, consider it a success
                  this.unviredSdk.logInfo("Resource", "downloadResources", 'SUCCESS - PDF directory exists');
                  this.showAlert("Resources downloaded successfully.");
                } catch (dirError) {
                  if (result === 0) {
                    this.unviredSdk.logInfo("Resource", "downloadResources",'SUCCESS');
                    this.showAlert("Resources downloaded successfully.");
                  } else {
                    this.unviredSdk.logInfo("Resource", "downloadResources", 'FAILED with code: ' + result);
                    this.showAlert("Failed to extract resources. Please try again.");
                  }
                }
              })
              .catch(err => {
                // Handle unzip error
                if (this.customizationAlert) {
                  this.customizationAlert.dismiss();
                }
                
                // Check if pdf directory exists despite unzip error
                this.file.checkDir(this.file.dataDirectory, "pdf")
                  .then(() => {
                    // If directory exists, resources might be available
                    this.unviredSdk.logInfo("Resource", "downloadResources", 'Unzip reported error but PDF directory exists');
                    this.showAlert("Resources may be available. Please try using them.");
                  })
                  .catch(() => {
                    this.showAlert("Error :" + JSON.stringify(err));
                  });
              });
          })
          .catch((err) => {
            // Handle file check error
            if (this.customizationAlert) {
              this.customizationAlert.dismiss();
            }
            this.showAlert("Error: Downloaded file not found. " + JSON.stringify(err));
          });
      }, (error) => {
        // Handle download error
        if (this.customizationAlert) {
          this.customizationAlert.dismiss();
        }
        this.showAlert("Error :" + JSON.stringify(error));
      });
    } catch (error) {
      // Handle any other errors
      if (this.customizationAlert) {
        this.customizationAlert.dismiss();
      }
      this.showAlert("Error :" + JSON.stringify(error));
    }
  }

  async showResourceDownloadLoading() {
    this.customizationAlert = await this.alertController.create({
      header: 'Alert',
      message: '<div><div class="imageStyle"><img src="./assets/img/blue-hourglass.gif"></div>' + 'Please wait while we configure resources. Do not exit this screen.</div>',
      backdropDismiss: false,
      cssClass: 'waitImage',
    });
    await this.customizationAlert.present();
    console.log("Download alert presented")
    this.unviredSdk.logDebug("home", "ionViewWillEnter", " Download alert presented ")
    await this.customizationAlert.onDidDismiss().then((data) => {          
    })
  }

  async showAlert(message) {
    const alert = await this.alertController.create({
      message: message,
      buttons: ['OK']
    });
    await alert.present();
  }
}
