import { Component, <PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { <PERSON>u<PERSON><PERSON><PERSON>er, PopoverController, LoadingController, AlertController, NavParams, NavController, ModalController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { AppConstant } from 'src/constants/appConstants';
import { InsightAIRopeDetectionPage } from '../Insight-AI/insight-ai-rope-detection/insight-ai-rope-detection.page';
import { AlertService } from '../services/alert.service';
import { CameraService } from '../services/camera.service';
import { DataService } from '../services/data.service';
import { HelpService } from '../services/help.service';
import { UtilserviceService } from '../services/utilservice.service';
import { VisionAIService } from '../services/vision-ai.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { HistoryComponentPage } from '../history-component/history-component.page';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faCircleCheck, faCircleExclamation, faCircleInfo, faEnvelope, faFloppyDisk, faGrip, faList, faListCheck, faTape, faTimes } from '@fortawesome/free-solid-svg-icons';
import { PlatformService } from '../services/platform.service';
declare var measurement;

@Component({
  selector: 'app-external-abrasion',
  templateUrl: './external-abrasion.page.html',
  styleUrls: ['./external-abrasion.page.scss'],
})
export class ExternalAbrasionPage implements OnInit {

  footerClose: boolean = true;
  isMeasurementEdit: boolean;
  startForm: FormGroup;
  measurementStart: any;
  inspectionHeader: any;
  startErrorMessage: string;
  fieldSegment: string = this.dataService.selectedSegment
  platformId: string = this.platformService.getPlatformId();
  measurementLength: any;
  lengthForm: FormGroup;
  lengthErrorMessage: string;
  readOnly: boolean = false;
  isStartValid: boolean = false;
  measurementEnd: any;
  endErrorMessage: string;
  endForm: FormGroup;
  locationOptionsList: any = [];
  layerOptionsList: any = []
  selectedLocationOption: any = ""
  selectedLayerOption: any = ""
  selectedTypeOfDamage: any;
  observationNote: any;
  yarnsPerStrandErrorMessage: string;
  noOfStrandsCtrlErrorMessage: string;
  brokenYarnErrorMessage: string;
  measurementId: any;
  measurementDate: any;
  continueWithoutImage: boolean = false;
  selectedMeasurement: any;
  measurementImageList: any;
  dirty: boolean = false;
  historyObject: any;
  readFlag: any;
  filteredvalues: any;
  externalRange: any = 0;
  damageType: any;
  product: any;
  selectedProduct: any;
  maxRating: any;
  pageComponent: any;
  legOptinsList: any;
  supportStatus: any = 'pass'
  lengthType = 'External Abrasion';
  cameraStarted: boolean = false;
  showRiskRating: boolean = false;
  riskRatingCondition: any;
  inspectionType : string = 'VisionAI';
  abrasionRangeDisabled : boolean = false;
  visionButtonDisabled : boolean = false;
  externalAIRange: any = 0;
  insightAIImage: any;
  tiledImagesArr:any[]=[];
  isLoggedInUserEmployee:boolean = false;
  isEmpEditConfig:boolean=false

  constructor(public menu: MenuController,
    public platformService: PlatformService,
    private popoverController: PopoverController,
    public loadingController: LoadingController,
    public alertController: AlertController,
    private translate: TranslateService,
    public formBuilder: FormBuilder,
    public helpService: HelpService,
    private navParams: NavParams,
    private router: Router,
    private ngZone: NgZone,
    private utilityService: UtilserviceService,
    public cameraService: CameraService,
    public dataService: DataService,
    public navCtrl: NavController,
    public alertService: AlertService,
    private unviredSDK: UnviredCordovaSDK,
    public device: Device,
    public faIconLibrary :FaIconLibrary,
    private visionAIService:VisionAIService,
    private modalController : ModalController) {

    this.faIconLibrary.addIcons(faBars, faEnvelope, faListCheck, faGrip, faTimes, faCircleInfo, faList, faTape, faCircleCheck, faCircleExclamation, faFloppyDisk)

    this.pageComponent = this;
    this.inspectionHeader = this.utilityService.getSelectedInspectionHeader();
    this.isMeasurementEdit = this.utilityService.getMeasurementEditMode();
    this.locationOptionsList = this.dataService.getLocationOptions();
    this.layerOptionsList = this.dataService.getLayerOptions();
    this.legOptinsList = this.dataService.getLegOptions();
    this.selectedLocationOption = (this.legOptinsList == undefined || this.legOptinsList.length == 0) ? '' : this.legOptinsList[0]
    this.selectedLayerOption = (this.layerOptionsList == undefined || this.layerOptionsList.length == 0) ? '' : this.layerOptionsList[0]

    this.measurementDate = this.utilityService.currentDate();
    if (this.inspectionHeader.MANUFACTURER != 'SAMSON' && this.inspectionHeader.MANUFACTURER != 'Samson' && this.inspectionHeader.MANUFACTURER != 'samson' ) {
      this.supportStatus = 'pass';
    }
    this.checkRiskRatingCondition();
    this.isLoggedInUserEmployee =  this.dataService.selectedRole!='Customer' ? true : false;

  }

  onFocusUserInputField(ev: any) {
    this.observationNote = this.helpService.moveTextAreaCursorToEndForWindows(ev);
  }

  async ngOnInit() {
    window.addEventListener('keyboardDidHide', () => {
      this.footerClose = true;
    });
    window.addEventListener('keyboardWillShow', (event) => {
      this.footerClose = false;
    });
    this.isEmpEditConfig = this.navParams.get('isEmpEditConfig');
    // Initialize readFlag to check if we're in read-only mode (viewing completed inspections)
    this.readFlag = this.utilityService.getObservationReadFlag();
    // initialize form controls
    this.startForm = this.formBuilder.group({
      measurementStartCtrl: ['', Validators.required],
      helpMeasurementStartCtrl: [{ value: '', disabled: true }, Validators.required]
    });
    this.lengthForm = this.formBuilder.group({
      measurementLengthCtrl: [{ value: '', disabled: true }, Validators.required],
      helpMeasurementLengthCtrl: [{ value: '', disabled: true }, Validators.required],
    });
    this.endForm = this.formBuilder.group({
      measurementEndCtrl: [{ value: '', disabled: true }, Validators.required],
      helpMeasurementEndCtrl: [{ value: '', disabled: true }, Validators.required],
    });
    if (this.isMeasurementEdit) {
      this.selectedMeasurement = this.utilityService.getSelectedMeasurement();
      this.measurementImageList = this.selectedMeasurement.externalImage;
      this.measurementStart = this.selectedMeasurement.start;
      this.measurementEnd = this.selectedMeasurement.end;
      this.measurementId = this.selectedMeasurement.id;
      // & set inspection type and result image path and score start
      if(this.selectedMeasurement.isFinalValuemanual!=undefined && !this.selectedMeasurement.isFinalValuemanual) {
        let index=0;
        this.inspectionType = 'VisionAI';
        if(this.selectedMeasurement.externalImage.length>0) {
          for(let i=0;i<this.selectedMeasurement.externalImage.length;i++) {
            if((this.selectedMeasurement.externalImage[i].tiledImage == undefined || false ) && this.selectedMeasurement.externalImage[i].mode=="insightAI") {
              index=i;
            }
          }
          this.externalAIRange = this.selectedMeasurement.externalImage[index].insightAIScore;
          console.log(this.selectedMeasurement.externalImage[index].Image.changingThisBreaksApplicationSecurity);
          
          // let fileName= this.selectedMeasurement.externalImage[index].Image.changingThisBreaksApplicationSecurity.substring(this.selectedMeasurement.externalImage[index].Image.changingThisBreaksApplicationSecurity.lastIndexOf('/')+1);
          // let path = this.selectedMeasurement.externalImage[index].Image.changingThisBreaksApplicationSecurity.substring(0,this.selectedMeasurement.externalImage[index].Image.changingThisBreaksApplicationSecurity.lastIndexOf('/'));
          // let editedImg =[];
          if(this.device.platform=='browser') {
            this.insightAIImage = this.selectedMeasurement.externalImage[index].Image;
          } else {
            this.insightAIImage = this.selectedMeasurement.externalImage[index].Image.changingThisBreaksApplicationSecurity;
          }
          // console.log("this.measurementImageList:",this.measurementImageList)
          for( var i = 0 ; i < this.measurementImageList.length; i++) {
            // this.measurementImageList = this.measurementImageList.filter(ele=>ele.tiledImage==undefined)
            // console.log("path image=>",this.measurementImageList[i]);
            if(this.measurementImageList[i].tiledImage==undefined) {
              if(this.platformId == 'electron') {
                this.measurementImageList[i].Image = await this.cameraService.getNativeURL(this.measurementImageList[i].Image.changingThisBreaksApplicationSecurity)
              }
            }
          }
          // this.measurementImageList = editedImg;
          
        }
      } else {
        this.inspectionType = 'Manual';
        this.externalRange = this.selectedMeasurement.external;
        for( var i = 0 ; i < this.measurementImageList.length; i++) {
          if(this.platformId == 'electron') {
            this.measurementImageList[i].Image = await this.cameraService.getNativeURL(this.measurementImageList[i].Image.changingThisBreaksApplicationSecurity)
          }
        }
      }
      // & set inspection type and result image and score end

      this.measurementLength = this.selectedMeasurement.otherData.measurementLength;
      this.supportStatus = this.selectedMeasurement.otherData.supportStatus;
      this.observationNote = this.selectedMeasurement.otherData.observationNotes
      this.damageType = this.selectedMeasurement.other;
      if (this.selectedMeasurement.otherData != undefined && this.selectedMeasurement.otherData.layerOptions != undefined) {
        this.selectedLayerOption = this.selectedMeasurement.otherData.layerOptions;
      }
      if (this.selectedMeasurement.otherData != undefined && this.selectedMeasurement.otherData.locationOptions != undefined) {
        this.selectedLocationOption = this.selectedMeasurement.otherData.locationOptions
        for (var i = 0; i < this.legOptinsList.length; i++) {
          if (this.legOptinsList[i].item == this.selectedMeasurement.otherData.locationOptions) {
            this.selectedLocationOption = this.legOptinsList[i];
            break;
          }
        }
      }
      if (this.selectedMeasurement.otherData != undefined && this.selectedMeasurement.otherData.layerOptions != undefined) {
        for (var i = 0; i < this.layerOptionsList.length; i++) {
          if (this.layerOptionsList[i].item == this.selectedMeasurement.otherData.layerOptions) {
            this.selectedLayerOption = this.layerOptionsList[i];
            break;
          }
        }
      }
      this.isStartValid = true;
      this.lengthForm.controls['measurementLengthCtrl'].enable();
      this.endForm.controls['measurementEndCtrl'].enable();
    } else {
      this.inspectionType = 'Manual';
      this.measurementId = UtilserviceService.guid();
    }
    if (this.isMeasurementEdit === false) {
      this.measurementImageList = [];
      this.externalRange = 0;
      this.damageType = '';
    }
    this.cameraService.setData(this.measurementImageList);
    if (!this.isMeasurementEdit) {
      console.log(this.inspectionHeader);
      if(this.inspectionHeader.CONSTRUCTION=='12-Strand' && 
        this.inspectionHeader.MANUFACTURER=='Samson') {
        if((['HMSF (Class II)','HMPE (Class II)'].includes(this.inspectionHeader.PRODUCT_TYPE) && this.inspectionHeader.PRODUCT.includes('AMSTEEL')) ||
        this.inspectionHeader.PRODUCT_TYPE == 'Conventional Fiber (Class I)' && this.inspectionHeader.PRODUCT=='TENEX' ||
        this.inspectionHeader.PRODUCT.includes('K-100™')) {
          this.checkInsightAIEnabledForUser();
        }
      }
    }
  }

  async checkInsightAIEnabledForUser() {
    let insightAIEnabled = await this.dataService.userEnabledForInsightAI();
    if(insightAIEnabled) {
      // this.dataService.isUserEnabledForInsightAI = false;
      this.inspectionType = 'VisionAI';
      this.abrasionRangeDisabled = true;
      console.log(insightAIEnabled);
    } else {
      this.inspectionType = 'Manual';
      this.abrasionRangeDisabled = false;
    }
  }

  // Toggle menu on bottom menu button click
  openMenu() {
    this.menu.toggle('menu');
  }

  async historyPopover($event: Event): Promise<void> {
    this.utilityService.setHistoryData('External');
    const popover = await this.popoverController.create({
      component: HistoryComponentPage,
      event,
      showBackdrop: true,
      animated: true,
    });
    await popover.present();
    const result = await popover.onDidDismiss();
    this.historyObject = this.utilityService.getPopoverHistoryData();
    if (this.historyObject != null) {
      this.isMeasurementEdit = true;
      this.measurementImageList = this.historyObject.DATA.externalImage;
      this.cameraService.setData(this.measurementImageList);
      this.externalRange = this.historyObject.DATA.external;
      this.measurementStart = this.historyObject.DATA.start;
      this.measurementId = this.historyObject.DATA.id;
      this.measurementEnd = this.historyObject.DATA.end;
      this.observationNote = this.historyObject.DATA.otherData.observationNotes
      this.supportStatus = this.historyObject.DATA.otherData.supportStatus,
        this.measurementLength = parseFloat(this.historyObject.DATA.otherData.measurementLength);
      this.damageType = this.historyObject.DATA.other;
      if (this.historyObject.DATA.otherData != undefined && this.historyObject.DATA.otherData.layerOptions != undefined) {
        this.selectedLayerOption = this.historyObject.DATA.otherData.layerOptions;
      }
      if (this.historyObject.DATA.otherData != undefined && this.historyObject.DATA.otherData.locationOptions != undefined) {
        this.selectedLocationOption = this.historyObject.DATA.otherData.locationOptions
        for (var i = 0; i < this.legOptinsList.length; i++) {
          if (this.legOptinsList[i].item == this.historyObject.DATA.otherData.locationOptions) {
            this.selectedLocationOption = this.legOptinsList[i];
            break;
          }
        }
      }
      if (this.historyObject.DATA.otherData != undefined && this.historyObject.DATA.otherData.layerOptions != undefined) {
        for (var i = 0; i < this.layerOptionsList.length; i++) {
          if (this.layerOptionsList[i].item == this.historyObject.DATA.otherData.layerOptions) {
            this.selectedLayerOption = this.layerOptionsList[i];
            break;
          }
        }
      }
      this.utilityService.emptyPopoverHistoryData();
      this.isStartValid = true;
      this.lengthForm.controls['measurementLengthCtrl'].enable();
      this.endForm.controls['measurementEndCtrl'].enable();
    } else {
    }
  }

  goToLineTracker() {
    if (this.dirty) {
      if (!this.isMeasurementEdit) {
        if (this.measurementStart != undefined || this.measurementStart != '' ||
          this.damageType != undefined || this.damageType != '' ||
          this.externalRange != 0 ||
          this.observationNote != undefined || this.observationNote != '' ||
          this.measurementImageList.length != 0 || this.measurementEnd != undefined || this.measurementLength != undefined ||
          this.measurementEnd != '' || this.measurementLength != '') {
          this.utilityService.menuAlert('lineTracker', 'line-tracker-home')
        } else {
          this.dataService.navigateToLineTracker(this)
        }
      } else {
        const object = {
          id: this.measurementId,
          start: parseFloat(this.measurementStart),
          external: this.externalRange,
          internal: 0,
          internalImage: '',
          externalImage: this.cameraService.editedImg,
          isFinalValuemanual:(this.inspectionType=='Manual')?true:false,
          originalImages: this.cameraService.imageAndAnnotation,
          type: 'External',
          other: this.damageType,
          date: this.measurementDate,
          end: this.measurementEnd,
          observationType: '',
          level: undefined,
          linearDamageType: undefined,
          inspaectionPart: undefined,
          otherData: {
            damageType: this.damageType,
            measurementLength: parseFloat(this.measurementLength),
            layerOptions: this.selectedLayerOption.item,
            locationOptions: this.selectedLocationOption.item,
            ending: parseFloat(this.measurementEnd),
            supportStatus: this.supportStatus,
            observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
            fieldSegment: this.fieldSegment
          }
        };
        if (this.selectedMeasurement) {
          if (this.dataService.isEmpty(object.originalImages) || !this.dataService.isEmpty(object.originalImages)) {
            this.selectedMeasurement.originalImages = object.originalImages;
          }
        }

        if (this.deepEquals(this.selectedMeasurement, object)) {
          this.dataService.navigateToLineTracker(this)
        } else {
          this.utilityService.menuAlert('lineTracker', 'line-tracker-home')
        }
      }
    }
  }

  gotoHome() {
    if (this.dirty) {
      if (!this.isMeasurementEdit) {
        if (this.measurementStart != undefined || this.measurementStart != '' ||
          this.damageType != undefined || this.damageType != '' ||
          this.externalRange != 0 ||
          this.observationNote != undefined || this.observationNote != '' ||
          this.measurementImageList.length != 0 || this.measurementEnd != undefined || this.measurementLength != undefined ||
          this.measurementEnd != '' || this.measurementLength != '') {
          this.utilityService.menuAlert('home', 'home')
        } else {
          this.router.navigate(['home']);
        }
      } else {
        const object = {
          id: this.measurementId,
          start: parseFloat(this.measurementStart),
          external: this.externalRange,
          internal: 0,
          internalImage: '',
          externalImage: this.cameraService.editedImg,
          isFinalValuemanual:(this.inspectionType=='Manual')?true:false,
          originalImages: this.cameraService.imageAndAnnotation,
          type: 'External',
          other: this.damageType,
          date: this.measurementDate,
          end: this.measurementEnd,
          observationType: '',
          level: undefined,
          linearDamageType: undefined,
          inspaectionPart: undefined,
          otherData: {
            damageType: this.damageType,
            measurementLength: parseFloat(this.measurementLength),
            layerOptions: this.selectedLayerOption.item,
            locationOptions: this.selectedLocationOption.item,
            ending: parseFloat(this.measurementEnd),
            supportStatus: this.supportStatus,
            observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
            fieldSegment: this.fieldSegment
          }
        };
        if (this.selectedMeasurement) {
          if (this.dataService.isEmpty(object.originalImages) || !this.dataService.isEmpty(object.originalImages)) {
            this.selectedMeasurement.originalImages = object.originalImages;
          }
        }

        if (this.deepEquals(this.selectedMeasurement, object)) {
          this.router.navigate(['home']);
        } else {
          this.utilityService.menuAlert('home', 'home')
        }
      }
    }
  }
  gotoInspections() {
    if (!this.dirty) {
      if (!this.isMeasurementEdit) {
        if (this.measurementStart != undefined || this.measurementStart != '' ||
          this.damageType != undefined || this.damageType != '' ||
          this.externalRange != 0 ||
          this.observationNote != undefined || this.observationNote != '' ||
          this.measurementImageList.length != 0 || this.measurementEnd != undefined || this.measurementLength != undefined ||
          this.measurementEnd != '' || this.measurementLength != '') {
          this.utilityService.menuAlert('inspections', 'inspection-home')
        } else {
          this.router.navigate(['inspection-home']);
        }
      } else {
        const object = {
          id: this.measurementId,
          start: parseFloat(this.measurementStart),
          external: this.externalRange,
          internal: 0,
          internalImage: '',
          externalImage: this.cameraService.editedImg,
          isFinalValuemanual:(this.inspectionType=='Manual')?true:false,
          originalImages: this.cameraService.imageAndAnnotation,
          type: 'External',
          other: this.damageType,
          date: this.measurementDate,
          end: this.measurementEnd,
          observationType: '',
          level: undefined,
          linearDamageType: undefined,
          inspaectionPart: undefined,
          otherData: {
            damageType: this.damageType,
            measurementLength: parseFloat(this.measurementLength),
            layerOptions: this.selectedLayerOption.item,
            locationOptions: this.selectedLocationOption.item,
            ending: parseFloat(this.measurementEnd),
            supportStatus: this.supportStatus,
            observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
            fieldSegment: this.fieldSegment
          }
        };
        if (this.selectedMeasurement) {
          if (this.dataService.isEmpty(object.originalImages) || !this.dataService.isEmpty(object.originalImages)) {
            this.selectedMeasurement.originalImages = object.originalImages;
          }
        }

        if (this.deepEquals(this.selectedMeasurement, object)) {
          this.router.navigate(['inspection-home']);
        } else {
          this.utilityService.menuAlert('inspections', 'inspection-home')
        }
      }
    } else {
      this.utilityService.menuAlert('inspections', 'inspection-home')
    }
  }
  gotoResources() {
    if(this.device.platform == "browser") {
      this.dataService.gotoResources();
      return;
    }
    if (!this.dirty) {
      if (!this.isMeasurementEdit) {
        if (this.measurementStart != undefined || this.measurementStart != '' ||
          this.damageType != undefined || this.damageType != '' ||
          this.externalRange != 0 ||
          this.observationNote != undefined || this.observationNote != '' ||
          this.measurementImageList.length != 0 || this.measurementEnd != undefined || this.measurementLength != undefined ||
          this.measurementEnd != '' || this.measurementLength != '') {
          this.utilityService.menuAlert('resources', 'resource')
        } else {
          this.router.navigate(['resource']);
        }
      } else {
        const object = {
          id: this.measurementId,
          start: parseFloat(this.measurementStart),
          external: this.externalRange,
          internal: 0,
          internalImage: '',
          externalImage: this.cameraService.editedImg,
          isFinalValuemanual:(this.inspectionType=='Manual')?true:false,
          originalImages: this.cameraService.imageAndAnnotation,
          type: 'External',
          other: this.damageType,
          date: this.measurementDate,
          end: this.measurementEnd,
          observationType: '',
          level: undefined,
          linearDamageType: undefined,
          inspaectionPart: undefined,
          otherData: {
            damageType: this.damageType,
            measurementLength: parseFloat(this.measurementLength),
            layerOptions: this.selectedLayerOption.item,
            locationOptions: this.selectedLocationOption.item,
            ending: parseFloat(this.measurementEnd),
            supportStatus: this.supportStatus,
            observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
            fieldSegment: this.fieldSegment
          }
        };
        if (this.selectedMeasurement) {
          if (this.dataService.isEmpty(object.originalImages) || !this.dataService.isEmpty(object.originalImages)) {
            this.selectedMeasurement.originalImages = object.originalImages;
          }
        }

        if (this.deepEquals(this.selectedMeasurement, object)) {
          this.router.navigate(['resource']);
        } else {
          this.utilityService.menuAlert('resources', 'resource')
        }
      }
    } else {
      this.utilityService.menuAlert('resources', 'resource')
    }
  }
  gotoContact() {
    if(this.device.platform == "browser") {
      this.dataService.gotoContact();
      return;
    }
    if (!this.dirty) {
      if (!this.isMeasurementEdit) {
        if (this.measurementStart != undefined || this.measurementStart != '' ||
          this.damageType != undefined || this.damageType != '' ||
          this.externalRange != 0 ||
          this.observationNote != undefined || this.observationNote != '' ||
          this.measurementImageList.length != 0 || this.measurementEnd != undefined || this.measurementLength != undefined ||
          this.measurementEnd != '' || this.measurementLength != '') {
          this.utilityService.menuAlert('contact', 'contact')
        } else {
          this.router.navigate(['contact']);
        }
      } else {
        const object = {
          id: this.measurementId,
          start: parseFloat(this.measurementStart),
          external: this.externalRange,
          internal: 0,
          internalImage: '',
          externalImage: this.cameraService.editedImg,
          isFinalValuemanual:(this.inspectionType=='Manual')?true:false,
          originalImages: this.cameraService.imageAndAnnotation,
          type: 'External',
          other: this.damageType,
          date: this.measurementDate,
          end: this.measurementEnd,
          observationType: '',
          level: undefined,
          linearDamageType: undefined,
          inspaectionPart: undefined,
          otherData: {
            damageType: this.damageType,
            measurementLength: parseFloat(this.measurementLength),
            layerOptions: this.selectedLayerOption.item,
            locationOptions: this.selectedLocationOption.item,
            ending: parseFloat(this.measurementEnd),
            supportStatus: this.supportStatus,
            observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
            fieldSegment: this.fieldSegment
          }
        };
        if (this.selectedMeasurement) {
          if (this.dataService.isEmpty(object.originalImages) || !this.dataService.isEmpty(object.originalImages)) {
            this.selectedMeasurement.originalImages = object.originalImages;
          }
        }

        if (this.deepEquals(this.selectedMeasurement, object)) {
          this.router.navigate(['contact']);
        } else {
          this.utilityService.menuAlert('contact', 'contact')
        }
      }
    } else {
      this.utilityService.menuAlert('contact', 'contact')
    }
  }

  ionViewDidEnter() {
    this.cameraStarted = false;
    this.readFlag = this.utilityService.getObservationReadFlag();
    if (this.readFlag === 'readOnly' && this.isMeasurementEdit === true && this.inspectionHeader.INSPECTION_STATUS!=AppConstant.REOPENED) {
      this.readOnly = true;
      this.disableFormFields();
    }
    this.filteredvalues = this.utilityService.getAllData().filter(t => t.DATA.type === 'External');
    this.product = this.inspectionHeader.PRODUCT;
    // if ((this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel Blue').toLowerCase()) > -1) ||
    //   (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-Blue').toLowerCase()) > -1) ||
    //   (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel®-Blue').toLowerCase()) > -1) ||
    //   (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('Amsteel-blue®').toLowerCase()) > -1) ||
    //   (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('AS-75').toLowerCase()) > -1) ||
    //   (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('AS-78 Uncoated').toLowerCase()) > -1) ||
    //   (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('AS-78').toLowerCase()) > -1) ||
    //   (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('AmSteel-Blue Uncoated').toLowerCase()) > -1) ||
    //   (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('AmSteel-Blue').toLowerCase()) > -1) ||
    //   (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('AmSteel-X').toLowerCase()) > -1) ||
    //   (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('AmSteel-Blue Whoopie Sling').toLowerCase()) > -1) ||
    //   (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('Saturn-12').toLowerCase()) > -1)) {
    if((this.inspectionHeader.INDUSTRY=='Mooring (CM)' && this.inspectionHeader.APPLICATION != 'Mooring Tails') || (this.inspectionHeader.INDUSTRY=='Utility (Industrial)') || (this.inspectionHeader.INDUSTRY =='Workboat (CM)' && this.inspectionHeader.APPLICATION=='Tug Mainline'))   {
      if((this.inspectionHeader.PRODUCT_TYPE == 'HMSF (Class II)' || this.inspectionHeader.PRODUCT_TYPE=='HMPE (Class II)') && this.inspectionHeader.CONSTRUCTION == "12-Strand") {
        this.product = 'AMSTEEL-BLUE'
        if (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('Saturn-12').toLowerCase()) > -1) {
          this.selectedProduct = 'SATURN-12'
        } else {
          this.selectedProduct = 'AMSTEEL-BLUE'
        }      
        this.maxRating = '7';
      } else if(this.inspectionHeader.PRODUCT_TYPE == 'Conventional Fiber (Class I)' && this.inspectionHeader.CONSTRUCTION == "12-Strand") {
        this.selectedProduct = "TENEX"
        this.product = 'TENEX'
        this.maxRating = '4';
      } else if ((this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('K ™ 100').toLowerCase()) > -1) ||
        (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('KZ-100').toLowerCase()) > -1) ||
        (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('K100®').toLowerCase()) > -1) ||
        (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100®').toLowerCase()) > -1) ||
        (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('K-100').toLowerCase()) > -1) ||
        (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('K100').toLowerCase()) > -1)) {
        this.product = 'K ™ 100'
        this.selectedProduct = 'K-100'
        this.maxRating = '7';
      } 
      // else if ((this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('Link-It').toLowerCase()) > -1) ||
      // (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('Link It').toLowerCase()) > -1) ||
      // (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('LinkIt').toLowerCase()) > -1)) {
      //   this.selectedProduct = 'AMSTEEL-BLUE'
      //   this.product = 'Link-It'
      //   this.maxRating = '7';
      // } 
      else {
        this.selectedProduct = ''
        this.maxRating = '';
      }
    } else if(this.inspectionHeader.PRODUCT_TYPE == 'Conventional Fiber (Class I)' && this.inspectionHeader.CONSTRUCTION == "12-Strand") {
      this.selectedProduct = "TENEX"
      this.product = 'TENEX'
      this.maxRating = '4'; 
    } 
    // else if ((this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('Link-It').toLowerCase()) > -1) ||
    //   (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('Link It').toLowerCase()) > -1) ||
    //   (this.inspectionHeader.PRODUCT.toLowerCase().indexOf(('LinkIt').toLowerCase()) > -1)) {
    //   this.selectedProduct = 'AMSTEEL-BLUE'
    //   this.product = 'Link-It'
    //   this.maxRating = '7';
    // } 
    else {
      this.selectedProduct = ''
      this.maxRating = '';
    }
  }

  ionViewDidLeave() {
    this.helpService.exitHelpMode();
    if(this.cameraStarted == false) {
      this.measurementImageList = [];
      this.externalRange = 0;
      this.measurementStart = null;
      this.measurementEnd = null;
      this.damageType = '';
    }
  }

  hasErrorStart(selectedField) {
    switch (selectedField) {
      case 'start':
        if (parseFloat(this.measurementStart) > this.inspectionHeader.INSPECTED_LENGTH) {
          this.startForm.controls['measurementStartCtrl'].setErrors({ 'incorrect': true })
          this.startErrorMessage = "Start value cannot be greater than inspected length " + this.inspectionHeader.INSPECTED_LENGTH
          return true;
        } else if (parseFloat(this.measurementStart) < 0) {
          this.startForm.controls['measurementStartCtrl'].setErrors({ 'incorrect': true })
          this.startErrorMessage = "Start value cannot be less than 0"
          return true;
        } else if ((this.measurementStart == '' && this.measurementStart != 0) || this.measurementStart == null || this.measurementStart == undefined) {
          this.startForm.controls['measurementStartCtrl'].setErrors({ 'incorrect': true })
          this.startErrorMessage = "Start value is mandatory"
          return true;
        } else if (isNaN(parseFloat(this.measurementStart))) {
          this.startForm.controls['measurementStartCtrl'].setErrors({ 'incorrect': true })
          this.startErrorMessage = "Invalid start value"
          return true;
        } else {
          return false;
        }
        break;
      case 'length':
        if (parseFloat(this.measurementLength) + parseFloat(this.measurementStart) > this.inspectionHeader.INSPECTED_LENGTH) {
          this.lengthForm.controls['measurementLengthCtrl'].setErrors({ 'incorrect': true })
          this.lengthErrorMessage = "Measurement Length should not be greater than inspected length " + this.inspectionHeader.INSPECTED_LENGTH
          return true;
        } else if (parseFloat(this.measurementLength) <= 0) {
          this.lengthForm.controls['measurementLengthCtrl'].setErrors({ 'incorrect': true })
          this.lengthErrorMessage = "Length should be greater than 0"
          return true;
        } else if (this.measurementLength == '' || this.measurementLength == null || this.measurementLength == undefined) {
          this.lengthForm.controls['measurementLengthCtrl'].setErrors({ 'incorrect': true })
          this.lengthErrorMessage = "Length value is mandatory"
          return true;
        } else if (isNaN(parseFloat(this.measurementLength))) {
          this.lengthForm.controls['measurementLengthCtrl'].setErrors({ 'incorrect': true })
          this.lengthErrorMessage = "Invalid length value"
          return true;
        } else {
          return false
        }
        break;
      case 'end':
        if (parseFloat(this.measurementEnd) > this.inspectionHeader.INSPECTED_LENGTH) {
          this.endForm.controls['measurementEndCtrl'].setErrors({ 'incorrect': true })
          this.endErrorMessage = "Measurement end should not be greater than inspected length " + this.inspectionHeader.INSPECTED_LENGTH
          return true;
        } else if (parseFloat(this.measurementEnd) <= parseFloat(this.measurementStart)) {
          this.endForm.controls['measurementEndCtrl'].setErrors({ 'incorrect': true })
          this.endErrorMessage = "End should be greater than start " + this.measurementStart
          return true;
        } else if (parseFloat(this.measurementEnd) <= 0) {
          this.endForm.controls['measurementEndCtrl'].setErrors({ 'incorrect': true })
          this.endErrorMessage = "End should be greater than start " + this.measurementStart
          return true;
        } else if (this.measurementEnd == '' || this.measurementEnd == null || this.measurementEnd == undefined) {
          this.endForm.controls['measurementEndCtrl'].setErrors({ 'incorrect': true })
          this.endErrorMessage = "End value is mandatory"
          return true;
        } else if (isNaN(parseFloat(this.measurementEnd))) {
          this.endForm.controls['measurementEndCtrl'].setErrors({ 'incorrect': true })
          this.endErrorMessage = "Invalid end value"
          return true;
        } else return false;
        break;
    }
  }

  keyPressed(event: any, value: any, setEnding?: boolean) {
    console.log("key pressed")
    if (event.key != "Backspace") {
      if (value && value != null) {
        var tempStart = value.toString() + event.key;
        if (!(/^([0-9]+)?([.]?[0-9]{0,3})?$/.test(tempStart))) {
          console.log("key pressed" + value)
          return false;
        }
      } else {
        if (!(/^([0-9]+)?([.]?[0-9]{0,3})?$/).test(event.key)) {
          console.log("key pressed" + value)
          return false;
        }
      }
    }
  }

  onChangeDisable(selectedField) {
    switch (selectedField) {
      case 'start':
        if (this.measurementStart != '' && this.startForm.controls['measurementStartCtrl'].valid) {
          this.lengthForm.controls['measurementLengthCtrl'].enable()
          this.endForm.controls['measurementEndCtrl'].enable()
          if (this.fieldSegment == 'length') {
            if (this.measurementLength != '' && this.lengthForm.controls['measurementLengthCtrl'].valid) {
              this.measurementEnd = (isNaN(parseFloat(this.measurementStart) + parseFloat(this.measurementLength)) || (parseFloat(this.measurementStart) + parseFloat(this.measurementLength)) < 0) ? '0' : (((parseFloat(this.measurementStart) * 1000) + (parseFloat(this.measurementLength) * 1000)) / 1000).toString();
            }
          } else if (this.fieldSegment == 'end') {
            if (this.measurementEnd != '' && this.endForm.controls['measurementEndCtrl'].valid) {
              this.measurementLength = (isNaN(parseFloat(this.measurementEnd) - parseFloat(this.measurementStart)) || (parseFloat(this.measurementEnd) - parseFloat(this.measurementStart)) < 0) ? '0' : (((parseFloat(this.measurementEnd) * 1000) - (parseFloat(this.measurementStart) * 1000)) / 1000).toString();
            }
          }
          this.isStartValid = true;
        } else {
          this.lengthForm.controls['measurementLengthCtrl'].disable()
          this.endForm.controls['measurementEndCtrl'].disable()
          this.measurementEnd = '';
          this.measurementLength = '';
          this.isStartValid = false;
        }
        break;
      case 'length':
        this.measurementEnd = (isNaN(parseFloat(this.measurementStart) + parseFloat(this.measurementLength)) || (parseFloat(this.measurementStart) + parseFloat(this.measurementLength)) < 0) ? '0' : (((parseFloat(this.measurementStart) * 1000) + (parseFloat(this.measurementLength) * 1000)) / 1000).toString();
        break;
      case 'end':
        this.measurementLength = (isNaN(parseFloat(this.measurementEnd) - parseFloat(this.measurementStart)) || (parseFloat(this.measurementEnd) - parseFloat(this.measurementStart)) < 0) ? '0' : (((parseFloat(this.measurementEnd) * 1000) - (parseFloat(this.measurementStart) * 1000)) / 1000).toString();
        break;
    }
  }

  async presentLoading() {
    const loading = await this.loadingController.create({
      spinner: 'circles',
      message: this.translate.instant('Please wait...'),
      translucent: true,
      mode: 'ios',
      duration: 2000
    });
    return await loading.present();
  }

  disableFormFields() {
    if (this.helpService.helpMode == true || this.readOnly == true) {
      this.startForm.controls['measurementStartCtrl'].disable();
      this.endForm.controls['measurementEndCtrl'].disable();
      this.lengthForm.controls['measurementLengthCtrl'].disable()
    } else {
      this.startForm.controls['measurementStartCtrl'].enable();
      if (this.isStartValid == true) {
        this.endForm.controls['measurementEndCtrl'].enable();
        this.lengthForm.controls['measurementLengthCtrl'].enable()
      }
    }
  }

  saveMeasurement() {
    if (this.startForm.controls['measurementStartCtrl'].valid == false && this.measurementStart != 0) {
      this.alertService.showAlert("", this.translate.instant("Please fill all the fields to continue"));
      return
    } else if (this.fieldSegment == 'length' && this.lengthForm.controls['measurementLengthCtrl'].valid == false) {
      this.alertService.showAlert("", this.translate.instant("Please fill all the fields to continue"));
      return
    } else if (this.fieldSegment == 'end' && this.endForm.controls['measurementEndCtrl'].valid == false) {
      this.alertService.showAlert("", this.translate.instant("Please fill all the fields to continue"));
      return
    }
    // if (this.observationNote == undefined || this.observationNote == '') {
    //   this.alertService.showAlert("", this.translate.instant("Please fill all the fields to continue"));
    //   return
    // }
    if (this.cameraService.editedImg.length == 0 && this.continueWithoutImage == false) {
      this.invalidImageAlert();
    } else if (this.isMeasurementEdit) {
      const obj = {
        id: this.measurementId,
        start: parseFloat(this.measurementStart),
        external: this.externalRange,
        externalAIScore:this.externalAIRange,
        isFinalValuemanual:(this.inspectionType=='Manual')?true:false,
        internal: 0,
        internalImage: '',
        externalImage: this.cameraService.editedImg,
        originalImages: this.cameraService.imageAndAnnotation,
        type: 'External',
        other: this.damageType,
        date: this.measurementDate,
        end: this.measurementEnd,
        observationType: '',
        otherData: {
          damageType: this.damageType,
          measurementLength: parseFloat(this.measurementLength),
          layerOptions: this.selectedLayerOption.item,
          locationOptions: this.selectedLocationOption.item,
          ending: parseFloat(this.measurementEnd),
          supportStatus: this.supportStatus,
          observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
          fieldSegment: this.fieldSegment
        }
      };
      this.validateDataAndSave(obj)
    } else if (!this.isMeasurementEdit) {
      if (parseFloat(this.measurementStart) > parseFloat(this.inspectionHeader.INSPECTED_LENGTH) && parseFloat(this.measurementEnd) > parseFloat(this.inspectionHeader.INSPECTED_LENGTH)) {
        this.utilityService.startEndValidation(this.inspectionHeader.INSPECTED_LENGTH);
      } else if (parseFloat(this.measurementStart) > parseFloat(this.measurementEnd)) {
        this.utilityService.startValidation();
      } else if (parseFloat(this.measurementEnd) > parseFloat(this.inspectionHeader.INSPECTED_LENGTH)) {
        this.utilityService.endValidation(parseFloat(this.inspectionHeader.INSPECTED_LENGTH));;
      } else if (parseFloat(this.measurementStart) === parseFloat(this.measurementEnd)) {
        this.utilityService.totallengthSameValidation();
      } else if (parseFloat(this.measurementStart) <= parseFloat(this.measurementEnd) && ((parseFloat(this.measurementStart) + parseFloat(this.measurementLength)) <= parseFloat(this.inspectionHeader.INSPECTED_LENGTH))) {
        const obj2 = {
          id: this.measurementId,
          start: parseFloat(this.measurementStart),
          external: this.externalRange,
          externalAIScore:this.externalAIRange,
          isFinalValuemanual:(this.inspectionType=='Manual')?true:false,
          internal: 0,
          internalImage: '',
          externalImage: this.cameraService.editedImg,
          originalImages: this.cameraService.imageAndAnnotation,
          type: 'External',
          other: this.damageType,
          date: this.measurementDate,
          end: this.measurementEnd,
          observationType: '',
          otherData: {
            damageType: this.damageType,
            measurementLength: parseFloat(this.measurementLength),
            layerOptions: this.selectedLayerOption.item,
            locationOptions: this.selectedLocationOption.item,
            ending: parseFloat(this.measurementEnd),
            supportStatus: this.supportStatus,
            observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
            fieldSegment: this.fieldSegment
          }
        };
        this.validateDataAndSave(obj2)
      }
    }
  }

  saveOnPause() {
    if (this.isMeasurementEdit) {
      const obj = {
        id: this.measurementId,
        start: parseFloat(this.measurementStart),
        external: this.externalRange,
        internal: 0,
        internalImage: '',
        externalImage: this.cameraService.editedImg,
        isFinalValuemanual:(this.inspectionType=='Manual')?true:false,
        originalImages: this.cameraService.imageAndAnnotation,
        type: 'External',
        other: this.damageType,
        date: this.measurementDate,
        end: this.measurementEnd,
        observationType: '',
        otherData: {
          damageType: this.damageType,
          measurementLength: parseFloat(this.measurementLength),
          layerOptions: this.selectedLayerOption.item,
          locationOptions: this.selectedLocationOption.item,
          ending: parseFloat(this.measurementEnd),
          supportStatus: this.supportStatus,
          observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
          fieldSegment: this.fieldSegment
        }
      };
      this.dataService.saveMeasurements(obj);
    } else if (!this.isMeasurementEdit) {
      const obj2 = {
        id: this.measurementId,
        start: parseFloat(this.measurementStart),
        external: this.externalRange,
        internal: 0,
        internalImage: '',
        externalImage: this.cameraService.editedImg,
        isFinalValuemanual:(this.inspectionType=='Manual')?true:false,
        originalImages: this.cameraService.imageAndAnnotation,
        type: 'External',
        other: this.damageType,
        date: this.measurementDate,
        end: this.measurementEnd,
        observationType: '',
        otherData: {
          damageType: this.damageType,
          measurementLength: parseFloat(this.measurementLength),
          layerOptions: this.selectedLayerOption.item,
          locationOptions: this.selectedLocationOption.item,
          ending: parseFloat(this.measurementEnd),
          supportStatus: this.supportStatus,
          observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
          fieldSegment: this.fieldSegment
        }
      };
      this.dataService.saveMeasurements(obj2);

    }
  }



  takeMeasurement(selectedField) {
    measurement.takeMeasurement({ "UOM": this.dataService.selectedUom }, res => {
      // alert("MEASUREMENT VALUE = " + res);
      this.ngZone.run(() => {
        if (res != '') {
          switch (selectedField) {
            case 'start':
              this.measurementStart = res;
              this.endForm.controls['measurementEndCtrl'].enable();
              this.lengthForm.controls['measurementLengthCtrl'].enable();
              this.isStartValid = true;
              break;
            case 'end':
              this.measurementEnd = res;
              this.measurementLength = (isNaN(parseFloat(this.measurementEnd) - parseFloat(this.measurementStart)) || (parseFloat(this.measurementEnd) - parseFloat(this.measurementStart)) < 0) ? '0' : (((parseFloat(this.measurementEnd) * 1000) - (parseFloat(this.measurementStart) * 1000)) / 1000).toString();
              break;
            case 'length':
              this.measurementLength = res;
              this.measurementEnd = (isNaN(parseFloat(this.measurementStart) + parseFloat(this.measurementLength)) || (parseFloat(this.measurementStart) + parseFloat(this.measurementLength)) < 0) ? '0' : (((parseFloat(this.measurementStart) * 1000) + (parseFloat(this.measurementLength) * 1000)) / 1000).toString();
              break;
          }
        }
      });
    }, err => {
      this.unviredSDK.logError("Measurement", "TakeMeasurement", err);
    });
  }

  checkAIImgLatest(index:number) {
    if(this.cameraService.editedImg[index].mode=='insightAI') {
      if(this.cameraService.editedImg.filter(ele=>ele.mode=='insightAI').length>1) {
        let lastIndex =-1;
        for (let i = 0; i<this.cameraService.editedImg.length; i++) {
          if (this.cameraService.editedImg[i].mode === 'insightAI') {
            lastIndex = i;
          }
        }
        if(lastIndex!=-1 && index==lastIndex) {
          return false;
        } else {
          return true;
        }
      } else {
          return false;
      }
    } else {
      return true;
    }
  }

  async deleteImage(index) {
    const alert = await this.alertController.create({
      backdropDismiss: false,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      header: this.translate.instant('Are you sure?'),
      message: '<strong>' + this.translate.instant('You want to delete this image') + '</strong>!',
      buttons: [
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
          }
        }, {
          text: this.translate.instant('Okay'),
          handler: () => {
            this.setChanged();
            // this.measurementImageList = this.cameraService.editedImg
            // TODO
            // if(this.tiledImagesArr.length>0) {
            //   let orgFileName = this.measurementImageList[index].Image.changingThisBreaksApplicationSecurity.substring(this.measurementImageList[index].Image.changingThisBreaksApplicationSecurity.lastIndexOf('/')+1);
            //   orgFileName = orgFileName.substring(0,orgFileName.indexOf('.'));
            //   this.tiledImagesArr = this.tiledImagesArr.filter((ele)=>{
            //     let tileName = ele.Image.changingThisBreaksApplicationSecurity.substring(ele.Image.changingThisBreaksApplicationSecurity.lastIndexOf('/')+1);
            //     tileName = tileName.substring(0,tileName.indexOf('.'));
            //     return tileName.toLowerCase().indexOf(orgFileName.toLowerCase())===-1
            //   })
            // }
            this.measurementImageList.splice(index, 1);
            if (this.measurementImageList.length == 0) {
              this.measurementImageList = false;
              this.measurementImageList = [];
            }

            
            if (this.cameraService.imageAndAnnotation.originalImgList != undefined) {
              this.cameraService.imageAndAnnotation.originalImgList.splice(index, 1)
            }
            this.cameraService.setData(this.measurementImageList);
          }
        }
      ]
    });
    await alert.present();
  }

  setChanged() {
    this.dirty = true;
  }

  backButtonClicked() {
    if (!this.isMeasurementEdit) {
      if (this.measurementStart != undefined || this.damageType != '' || this.measurementImageList.length !== 0 || this.measurementEnd !== undefined || this.selectedLayerOption.item != this.layerOptionsList[0].item || this.selectedLocationOption.item != this.legOptinsList[0].item) {
        this.router.navigate(['external-abrasion']);
        this.utilityService.backAlert();
      } else {
        this.router.navigate(['new-observation']);
      }
    } else {
      var object = {
        id: this.measurementId,
        start: parseFloat(this.measurementStart),
        external: this.externalRange,
        internal: 0,
        internalImage: '',
        externalImage: this.cameraService.editedImg,
        isFinalValuemanual:(this.inspectionType=='Manual')?true:false,
        originalImages: this.cameraService.imageAndAnnotation,
        type: 'External',
        other: this.damageType,
        date: this.measurementDate,
        end: this.measurementEnd,
        observationType: '',
        level: undefined,
        linearDamageType: undefined,
        inspaectionPart: undefined,
        otherData: {
          damageType: this.damageType,
          measurementLength: parseFloat(this.measurementLength),
          layerOptions: this.selectedLayerOption.item,
          locationOptions: this.selectedLocationOption.item,
          ending: parseFloat(this.measurementEnd),
          supportStatus: this.supportStatus,
          observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
          fieldSegment: this.fieldSegment
        }
      };
      if (this.selectedLocationOption.item) {

        if (this.dataService.isEmpty(object.originalImages) || !this.dataService.isEmpty(object.originalImages)) {
          this.selectedMeasurement.originalImages = object.originalImages;
        }
      }

      if (this.deepEquals(this.selectedMeasurement, object)) {
        this.router.navigate(['observations']);
      } else {
        this.router.navigate(['external-abrasion']);
        this.utilityService.backAlert(true);
      }
    }

  }

  setRating(rating) {
    if (rating && rating != '') {
      this.externalRange = rating;
    }
  }




  setChangedRating() {
    if(this.selectedMeasurement != undefined) {
      if (this.externalRange != this.selectedMeasurement.external) {
        this.dirty = true;
      }
    } else {
      this.dirty = true;
    }
  }



  async invalidImageAlert() {
    var confAlert = await this.alertController.create({
      backdropDismiss: false,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      message: '<strong>' + this.translate.instant('No photo associated with observation, are you sure you want to continue?') + '</strong>',
      buttons: [
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
            this.continueWithoutImage = false;
          }
        }, {
          text: this.translate.instant('Continue'),
          role: 'continue',
          handler: () => {
            this.continueWithoutImage = true;
            this.saveMeasurement();
          }
        }
      ]
    });
    await confAlert.present();
  }

  async validateDataAndSave(object) {
    if (this.selectedMeasurement) {
      if (this.dataService.isEmpty(object.originalImages) || !this.dataService.isEmpty(object.originalImages)) {
        this.selectedMeasurement.originalImages = object.originalImages;
      }
      object.inspaectionPart = this.selectedMeasurement.inspaectionPart; object.linearDamageType = this.selectedMeasurement.linearDamageType;;
      this.selectedMeasurement.observationType = object.observationType
    }

    if (this.deepEquals(this.selectedMeasurement, object)) {
      this.alertService.showAlert("", this.translate.instant("No changes to save."))
    } else {
      if (isNaN(object.start) || isNaN(object.otherData.measurementLength) || isNaN(object.otherData.ending)) {
        this.alertService.showAlert("", this.translate.instant("Please enter valid values to save"))
        return;
      }
      if (parseFloat(object.start) > parseFloat(this.inspectionHeader.INSPECTED_LENGTH) || parseFloat(object.otherData.ending) > parseFloat(this.inspectionHeader.INSPECTED_LENGTH)) {
        this.alertService.showAlert("", this.translate.instant("Start and end should be less than inspection length") + " " + this.inspectionHeader.INSPECTED_LENGTH)
        return;
      }
      if (parseFloat(object.start) == parseFloat(object.otherData.ending)) {
        this.alertService.showAlert("", this.translate.instant("Inspection length cannot be 0"))
        return;
      }
      if (parseFloat(object.start) == parseFloat(object.otherData.ending)) {
        this.alertService.showAlert("", this.translate.instant("Inspection length cannot be 0"))
        return;
      }

      if (this.product == 'AMSTEEL-BLUE' || this.product == 'K ™ 100' || this.product == 'TENEX') {
        if (this.externalRange == 0 && this.externalAIRange ==0) {
          this.utilityService.validationAlert();
          return;
        }
      } else {
        if (this.damageType == "") {
          this.utilityService.validationAlert();
          return;
        }
      }
      if (object.externalImage.length == 0 && this.continueWithoutImage == false) {
        this.invalidImageAlert()
        return;
      }

      // TODO

      // let filteredInnerArrays;
      // if (object.externalImage.length > 0) {
      //   filteredInnerArrays = object.externalImage
      //     .filter(
      //       (obj) => obj.mode == "insightAI" && obj.tiledImages.length > 0
      //     )
      //     .map((obj) => {
      //       const innerArray = obj.tiledImages;
      //       delete obj.tiledImages;
      //       return innerArray;
      //     })
      //     .flat();
      // }
      
      // if( filteredInnerArrays.length>0) {
      //   object.externalImage = object.externalImage.concat(filteredInnerArrays)
      // }
      await this.dataService.saveMeasurements(object);

      if(this.isEmpEditConfig) {
        this.modalController.dismiss();
      } else {
        this.router.navigate(['observations']);
      }
      this.isMeasurementEdit = false;
      this.cameraService.reset();
    }

  }

  deepEquals(selectedMeasurement, currentMeasurement) {
    if (this.dirty == true || this.cameraService.measurementEdited == true) {
      return false;
    }
    if (selectedMeasurement === currentMeasurement) {
      return true; // if both x and currentMeasurement are null or undefined and exactly the same
    } else if (!(selectedMeasurement instanceof Object) || !(currentMeasurement instanceof Object)) {
      return false; // if they are not strictly equal, they both need to be Objects
    } else if (selectedMeasurement.constructor !== currentMeasurement.constructor) {
      // they must have the exact same prototype chain, the closest we can do is
      // test their constructor.
      return false;
    } else {
      for (const p in selectedMeasurement) {
        if (!selectedMeasurement.hasOwnProperty(p)) {
          continue; // other properties were tested using x.constructor === y.constructor
        }
        if (!currentMeasurement.hasOwnProperty(p)) {
          return false; // allows to compare x[ p ] and currentMeasurement[ p ] when set to undefined
        }
        if (selectedMeasurement[p] === currentMeasurement[p]) {
          continue; // if they have the same strict value or identity then they are equal
        }
        if (typeof (selectedMeasurement[p]) !== 'object') {
          return false; // Numbers, Strings, Functions, Booleans must be strictly equal
        }
        if (!this.deepEquals(selectedMeasurement[p], currentMeasurement[p])) {
          return false;
        }
      }
      for (const p in currentMeasurement) {
        if (currentMeasurement.hasOwnProperty(p) && !selectedMeasurement.hasOwnProperty(p)) {
          return false;
        }
      }
      return true;
    }
  }

  async checkRiskRatingCondition() {
    this.showRiskRating = false;
    this.riskRatingCondition = await this.unviredSDK.dbSelect("RISK_RATING_HEADER", "OBSERVATION_TYPE like 'External' AND PRODUCT_TYPE like '" + this.inspectionHeader.PRODUCT_TYPE + "' AND ROPE_TYPE like '" + this.inspectionHeader.CONSTRUCTION + "' AND ROPE_LAYER like '" + this.selectedLayerOption.item + "'")
    if(this.riskRatingCondition.type == ResultType.success) {
      if(this.riskRatingCondition.data.length > 0) {
        this.showRiskRating = true;
      }
    }
  }

  async layerChange(event) {
    this.checkRiskRatingCondition();
  }

  async presentResultModal(props?:any) {
    return new Promise(async (resolve,reject)=>{
      const resModal = await this.modalController.create({
        component: InsightAIRopeDetectionPage,
        backdropDismiss : false,
        componentProps: props,
        cssClass: 'resultmodal',
      });
      await resModal.present();
      resModal.onDidDismiss().then(async (data:any) => {
        resolve(data.data);
      }).catch(err=>{
        reject(err);
      });
    })
  }

  async openCamera() {
    this.visionAIService.screenMode = 'EXTERNAL';
    this.unviredSDK.logInfo("EXTERNAL_ABRASION","OPENCAMERA","OPENCAMERA METHOD START");
    console.log("inspectionHeader",this.inspectionHeader);
    let inspObject = JSON.parse(JSON.stringify(this.inspectionHeader));
    inspObject['PRODUCT_NAME'] = inspObject.PRODUCT;
    let res = await this.cameraService.takePictureVisionAI().then(async result=>{
      if(result!=undefined) {
        this.unviredSDK.logInfo("EXTERNAL_ABRASION","OPENCAMERA","EXTERNAL_ABRASION - SUCCESSFULLY RECIEVED IMAGE DATA FROM CAMERA/GALLERY");
        this.insightAIImage = result;
        this.visionAIService.selectedCertificate = inspObject;
        this.unviredSDK.logInfo("EXTERNAL_ABRASION","OPENCAMERA","EXTERNAL_ABRASION - PRESENTING INSIGHT AI RESULTS MODAL");
        this.presentResultModal().then(async (data:any)=>{
          if(data!=undefined) {
            // console.log(score);
            if(data.action=='ACCEPT') {
              this.unviredSDK.logInfo("EXTERNAL_ABRASION","OPENCAMERA","EXTERNAL_ABRASION - CLICKED ACCEPT FROM INSIGHT AI RESULTS MODAL");
              this.externalAIRange = data.score;
              // await this.alertService.present();
              this.unviredSDK.logInfo("EXTERNAL_ABRASION","OPENCAMERA","EXTERNAL_ABRASION - PRESENT LOADER");
              // TODO
              // this.tiledImagesArr = this.tiledImagesArr.concat(data.tiledImages);
              this.unviredSDK.logInfo("EXTERNAL_ABRASION","OPENCAMERA","EXTERNAL_ABRASION - WRITING ACCEPTED IMAGE TO FILE STORAGE START");
              await this.writeImageToStorage(data.imageURL,data.score,data.fileName+'.jpeg');
              this.unviredSDK.logInfo("EXTERNAL_ABRASION","OPENCAMERA","EXTERNAL_ABRASION - WRITING IMAGE TO FILE STORAGE COMPLETED");
              this.insightAIImage =  data.imageURL;
              this.cameraService.editedImg[this.cameraService.editedImg.length-1].insightAIScore = data.score;
              // TODO
              this.cameraService.editedImg[this.cameraService.editedImg.length-1].tiledImages = data.tiledImages;
              if(this.alertService.isLoading){
                this.unviredSDK.logInfo("EXTERNAL_ABRASION","OPENCAMERA","DISMISSING LOADER");
                await this.alertService.dismiss();
              }
            } else if(data.action=='RETAKE') {
              this.openCamera();
            } else if(data.action=='MANUAL') {
              this.inspectionType = 'Manual';
            }
          }
        })
      }}).catch(err=>{
        this.unviredSDK.logInfo("EXTERNAL_ABRASION","OPENCAMERA","ERROR PRESENTING INSIGHT AI RESULTS MODAL");
          console.log(err);
      });
  }

  async writeImageToStorage(imgURL:string,score:number,fileName?:string) {
    this.unviredSDK.logInfo("EXTERNAL_ABRASION","OPENCAMERA","WRITEIMAGETOFILESTORAGE METHOD START");
    await this.cameraService.writeFileToStorage(imgURL,'EXTERNAL_ABRASION',score,fileName);
  }

  async insightAiAlert() {
      var confAlert = await this.alertController.create({
        backdropDismiss: false,
        animated: true,
        mode: 'ios',
        header: "Warning!",
        keyboardClose: true,
        message: "Switching to Manual will lose the Insight AI data, Are you sure?",
        buttons: [
          {
            text: this.translate.instant('Cancel'),
            role: 'cancel',
            cssClass: 'secondary'
          }, {
            text: this.translate.instant('Ok'),
            role: 'continue'
          }
        ]
      });
      await confAlert.present();

      const { role }  = await confAlert.onDidDismiss();
    return role;
    }

  async visionTypeChanged(event:any) {
    console.log(event.target.value);
    if(event.target.value=='Manual') {
      if(this.inspectionType=='VisionAI') {
        if((<HTMLInputElement>event.target).checked==true) {
          if(this.externalAIRange>0) {
            (<HTMLInputElement>event.target).checked=false;
            this.abrasionRangeDisabled = true;
            this.inspectionType = 'VisionAI';
            // * showing alert to confirm switching from insightAI to manual when manual radio button is clicked
            await this.insightAiAlert().then(data=>{
              if(data=='cancel') {
                console.log('cancel');
              } else if(data=='continue') {
                this.inspectionType = 'Manual';
                (<HTMLInputElement>event.target).checked=true;
                this.abrasionRangeDisabled = false;
                this.externalAIRange = '';
                this.externalRange = 0;
                this.visionButtonDisabled = false;
              }
            })
            return;
          } else {
            this.inspectionType = 'Manual';
            (<HTMLInputElement>event.target).checked=true;
            this.abrasionRangeDisabled = false;
            this.externalRange = 0;
          }
        }
      } else if (this.inspectionType=='Manual') {
        if((<HTMLInputElement>event.target).checked==false) {
          (<HTMLInputElement>event.target).checked=true;
        }
      }
    } else if(event.target.value=='VisionAI') {
      if(this.inspectionType=='Manual') {
        if((<HTMLInputElement>event.target).checked==true) {
          if(this.cameraService.editedImg.length>0) {
            this.cameraService.editedImg = this.cameraService.editedImg.filter(ele=>ele.mode=='insightAI');
            let index;
            for(let i=0;i<this.cameraService.editedImg.length;i++) {
              if(this.cameraService.editedImg[i].mode=="insightAI") {
                index=i;
              }
            }
            if(index!=undefined) {
              this.externalAIRange = this.cameraService.editedImg[index].insightAIScore;
            } else {
              this.externalAIRange = 0;
            }
            
          }
          this.inspectionType = 'VisionAI';
          (<HTMLInputElement>event.target).checked=true;
          this.externalRange = 0;
          this.abrasionRangeDisabled = true;
        }
      } else if(this.inspectionType=='VisionAI') {
        if((<HTMLInputElement>event.target).checked==false) {
          (<HTMLInputElement>event.target).checked=true;
        }
      }
    }
  }

  async previewInsightAIResult() {
    this.visionAIService.imageData = this.insightAIImage;
    let props={screenType:'PREVIEWRESULTS',score: this.externalAIRange }
    await this.presentResultModal(props).then((data:any)=>{
      if(data!=undefined) {
        if(data.action=='RETAKE') {
          this.openCamera();
        }
      }
      console.log(data);
    }).catch(err=>{
      console.log(err);
    });
  }

  closeSpliceModal() {
    this.modalController.dismiss();
  }

  async saveAndCapture() {
    this.cameraStarted = true;
    this.cameraService.takePicture('','external', this.pageComponent, this.maxRating, this.externalRange, this.selectedProduct,false,this.isLoggedInUserEmployee); 
    this.setChanged()
  }

  async saveAndEdit(item:any,i:any) {
    if(item.mode!='insightAI') {
      this.cameraService.editImage(item.Image, i, 'external','', this.pageComponent, this.maxRating, this.externalRange, this.selectedProduct)
      this.setChanged();
    }
  }
}
