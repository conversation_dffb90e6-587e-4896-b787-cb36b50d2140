
// Ionicons Icon Font CSS
// --------------------------
// Ionicons CSS for Ionic's <ion-icon> element
// ionicons-icons.scss has the icons and their unicode characters

$ionicons-font-path: $font-path !default;

@import "ionicons-icons";
@import "ionicons-variables";


@font-face {
  font-family: "Ionicons";
  src: url("#{$ionicons-font-path}/ionicons.woff2?v=#{$ionicons-version}") format("woff2"),
    url("#{$ionicons-font-path}/ionicons.woff?v=#{$ionicons-version}") format("woff"),
    url("#{$ionicons-font-path}/ionicons.ttf?v=#{$ionicons-version}") format("truetype");
  font-weight: normal;
  font-style: normal;
}

ion-icon {
  display: inline-block;

  font-family: "Ionicons";
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 1;
  text-rendering: auto;
  text-transform: none;
  speak: none;

  @include rtl() {
    &[aria-label^="arrow"]::before,
    &[flip-rtl]::before {
      transform: scaleX(-1);
    }

    &[unflip-rtl]::before {
      transform: scaleX(1);
    }
  }

  &::before {
    display: inline-block;
  }
}
