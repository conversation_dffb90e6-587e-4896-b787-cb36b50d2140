import { Component, Ng<PERSON>one, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormControl, FormGroupDirective, NgForm } from '@angular/forms';
import { Router } from '@angular/router';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { MenuController, PopoverController, AlertController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { HistoryComponentPage } from '../history-component/history-component.page';
import { AlertService } from '../services/alert.service';
import { CameraService } from '../services/camera.service';
import { DataService } from '../services/data.service';
import { HelpService } from '../services/help.service';
import { UtilserviceService } from '../services/utilservice.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faEnvelope, faListCheck, faGrip, faTimes, faCircleInfo, faList, faTape, faCircleCheck, faCircleExclamation, faFloppyDisk } from '@fortawesome/free-solid-svg-icons';
import { ErrorStateMatcher } from '@angular/material/core';
import { PlatformService } from '../services/platform.service';

export class MyErrorStateMatcher implements ErrorStateMatcher {
  isErrorState(control: FormControl | null, form: FormGroupDirective | NgForm | null): boolean {
    const isSubmitted = form && form.submitted;
    return !!(control && control.invalid && (control.dirty || control.touched || isSubmitted));
  }
}

declare var measurement;

@Component({
  selector: 'app-wire-breaks',
  templateUrl: './wire-breaks.page.html',
  styleUrls: ['./wire-breaks.page.scss'],
})
export class WireBreaksPage implements OnInit {

  footerClose: boolean = true;
  isMeasurementEdit: boolean;
  startForm: FormGroup;
  measurementStart: any;
  inspectionHeader: any;
  startErrorMessage: string;
  fieldSegment: string = this.dataService.selectedSegment
  platformId: string = this.platformService.getPlatformId();
  measurementLength: any;
  lengthForm: FormGroup;
  lengthErrorMessage: string;
  readOnly: boolean = false;
  isStartValid: boolean = false;
  measurementEnd: any;
  endErrorMessage: string;
  endForm: FormGroup;
  noOfBreaksForm: FormGroup;
  locationOptionsList: any = [];
  layerOptionsList: any = []
  selectedLocationOption: any = ""
  selectedLayerOption: any = ""
  // selectedTypeOfDamage: any;
  damageLevel: any;
  // selectedTypeOfDamageString: string;
  observationNote: any;
  measurementId: any;
  measurementDate: any;
  continueWithoutImage: boolean = false;
  selectedMeasurement: any;
  measurementImageList: any;
  dirty: boolean = false;
  historyObject: any;
  readFlag: any;
  filteredvalues: any;
  noOfBreaks: any;
  noOfBreakesErrorMessage: any;
  legOptinsList: any;
  supportStatus: any = 'pass'
  lengthType = 'Wire Breake';
  cameraStarted: boolean = false;
  matcher:any;



  constructor(
    public platformService: PlatformService,
    public menu: MenuController,
    public helpService: HelpService,
    public formBuilder: FormBuilder,
    public utilityService: UtilserviceService,
    public cameraService: CameraService,
    public unviredSdk: UnviredCordovaSDK,
    public popoverController: PopoverController,
    public ngZone: NgZone,
    public alertController: AlertController,
    public translate: TranslateService,
    public router: Router,
    public alertService: AlertService,
    public dataService: DataService,
    public device: Device,
    public faIconLibrary : FaIconLibrary) {

    this.faIconLibrary.addIcons(faBars, faEnvelope, faListCheck, faGrip, faTimes, faCircleInfo, faList, faTape, faCircleCheck, faCircleExclamation, faFloppyDisk)
 
    // this.cameraService.reset();
    this.inspectionHeader = this.utilityService.getSelectedInspectionHeader();
    this.isMeasurementEdit = this.utilityService.getMeasurementEditMode();
    this.locationOptionsList = this.dataService.getLocationOptions();
    this.layerOptionsList = this.dataService.getLayerOptions();
    this.legOptinsList = this.dataService.getLegOptions();
    this.selectedLocationOption = (this.legOptinsList == undefined || this.legOptinsList.length == 0) ? '' : this.legOptinsList[0]
    this.selectedLayerOption = (this.layerOptionsList == undefined || this.layerOptionsList.length == 0) ? '' : this.layerOptionsList[0]
    this.dataService.setLoadBearingOption(this.inspectionHeader, this.selectedLayerOption.item)
    this.measurementDate = this.utilityService.currentDate();
    if (this.inspectionHeader.MANUFACTURER != 'SAMSON' && this.inspectionHeader.MANUFACTURER != 'Samson' && this.inspectionHeader.MANUFACTURER != 'samson' ) {
      this.supportStatus = 'pass';
    }

  }

  onFocusUserInputField(ev: any) {
    this.observationNote = this.helpService.moveTextAreaCursorToEndForWindows(ev);
  }

  async ngOnInit() {
    // Show/hide footer based on keyboard status
    window.addEventListener('keyboardDidHide', () => {
      this.footerClose = true;
    });
    window.addEventListener('keyboardWillShow', (event) => {
      this.footerClose = false;
    });

    // initialize form controls
    this.startForm = this.formBuilder.group({
      measurementStartCtrl: ['', Validators.required],
      helpMeasurementStartCtrl: [{ value: '', disabled: true }, Validators.required]
    });
    this.lengthForm = this.formBuilder.group({
      measurementLengthCtrl: [{ value: '', disabled: true }, Validators.required],
      helpMeasurementLengthCtrl: [{ value: '', disabled: true }, Validators.required],
    });
    this.endForm = this.formBuilder.group({
      measurementEndCtrl: [{ value: '', disabled: true }, Validators.required],
      helpMeasurementEndCtrl: [{ value: '', disabled: true }, Validators.required],
    });
    this.noOfBreaksForm = this.formBuilder.group({
      noOfBreaksCtrl: ['', [Validators.required,Validators.maxLength(2)]],

    });
    this.matcher = new MyErrorStateMatcher();

    if (this.isMeasurementEdit == true) {
      this.selectedMeasurement = this.utilityService.getSelectedMeasurement()
      this.measurementId = this.selectedMeasurement.id;
      this.measurementImageList = this.selectedMeasurement.externalImage;
      this.measurementStart = this.selectedMeasurement.start;
      this.measurementLength = parseFloat(this.selectedMeasurement.otherData.measurementLength)
      this.selectedLayerOption = this.selectedMeasurement.otherData.layerOptions;
      this.selectedLocationOption = this.selectedMeasurement.otherData.locationOptions
      for (var i = 0; i < this.legOptinsList.length; i++) {
        if (this.legOptinsList[i].item == this.selectedMeasurement.otherData.locationOptions) {
          this.selectedLocationOption = this.legOptinsList[i];
          break;
        }
      }
      for (var i = 0; i < this.layerOptionsList.length; i++) {
        if (this.layerOptionsList[i].item == this.selectedMeasurement.otherData.layerOptions) {
          this.selectedLayerOption = this.layerOptionsList[i];
          break;
        }
      }
      this.supportStatus = this.selectedMeasurement.otherData.supportStatus;
      this.measurementEnd = this.selectedMeasurement.otherData.ending;
      this.observationNote = this.selectedMeasurement.otherData.observationNotes
      this.fieldSegment = this.selectedMeasurement.otherData.fieldSegment
      this.noOfBreaks = this.selectedMeasurement.external
      this.lengthForm.controls['measurementLengthCtrl'].enable();
      this.endForm.controls['measurementEndCtrl'].enable();
      this.isStartValid = true;
      let editedImg =[];
      for( var i = 0 ; i < this.measurementImageList.length; i++) {
        // console.log("path image=>",this.measurementImageList[i]);
        if(this.platformId == 'electron') {
          let fixedURL = await this.cameraService.getNativeURL(this.measurementImageList[i].Image.changingThisBreaksApplicationSecurity);
          editedImg.push({"Image": fixedURL ,"mode": this.measurementImageList[i].mode})
        } else {
          editedImg.push({"Image": this.measurementImageList[i].Image,"mode": this.measurementImageList[i].mode})
        }
      }
      this.measurementImageList = editedImg;
    } else {
      this.measurementId = UtilserviceService.guid()
      this.measurementImageList = []
    }
    this.cameraService.setData(this.measurementImageList);
  }

  // Toggle menu on bottom menu button click
  openMenu() {
    this.menu.toggle('menu');
  }

  async historyPopover($event: Event): Promise<void> {
    this.utilityService.setHistoryData('Wire Breaks');
    const popover = await this.popoverController.create({
      component: HistoryComponentPage,
      event,
      showBackdrop: true,
      animated: true,
    });
    await popover.present();
    const result = await popover.onDidDismiss();
    this.historyObject = this.utilityService.getPopoverHistoryData();
    if (this.historyObject != null) {
      this.isMeasurementEdit = true;
      this.measurementImageList = this.historyObject.DATA.externalImage;
      this.cameraService.setData(this.measurementImageList);
      this.measurementId = this.historyObject.DATA.id;
      this.measurementStart = parseFloat(this.historyObject.DATA.start);
      this.measurementLength = parseFloat(this.historyObject.DATA.otherData.measurementLength);
      this.supportStatus = this.historyObject.DATA.otherData.supportStatus;
      this.selectedLayerOption = this.historyObject.DATA.otherData.layerOptions,
        this.noOfBreaks = this.historyObject.DATA.external,
        this.selectedLocationOption = this.historyObject.DATA.otherData.locationOptionsl;
      for (var i = 0; i < this.legOptinsList.length; i++) {
        if (this.legOptinsList[i].item == this.historyObject.DATA.otherData.locationOptions) {
          this.selectedLocationOption = this.legOptinsList[i];
          break;
        }
      }
      for (var i = 0; i < this.layerOptionsList.length; i++) {
        if (this.layerOptionsList[i].item == this.historyObject.DATA.otherData.layerOptions) {
          this.selectedLayerOption = this.layerOptionsList[i];
          break;
        }
      }
      this.damageLevel = this.historyObject.DATA.level;
      this.observationNote = this.historyObject.DATA.otherData.observationNotes
      this.fieldSegment = this.historyObject.DATA.otherData.fieldSegment
      this.lengthForm.controls['measurementLengthCtrl'].enable();
      this.endForm.controls['measurementEndCtrl'].enable();
      this.measurementEnd = this.historyObject.DATA.otherData.ending;
      this.utilityService.emptyPopoverHistoryData();
      this.isStartValid = true;
    } else {

    }
  }
  goToLineTracker() {
    if (!this.isMeasurementEdit) {
      if (this.measurementStart != undefined || this.measurementStart != '' ||
        this.damageLevel != undefined || this.damageLevel != '' ||
        this.observationNote != undefined || this.observationNote != '' ||
        this.measurementImageList.length != 0 || this.measurementEnd != undefined || this.measurementLength != undefined ||
        this.measurementEnd != '' || this.measurementLength != '' ||
        this.noOfBreaks != undefined || this.noOfBreaks != '') {
        this.utilityService.menuAlert('lineTracker', 'line-tracker-home')
      } else {
        this.dataService.navigateToLineTracker(this)
      }
    } else {
      var object = {
        id: this.measurementId,
        end: null,
        external: this.noOfBreaks,
        start: parseFloat(this.measurementStart),
        internal: 0,
        internalImage: '',
        externalImage: this.cameraService.editedImg,
        originalImages: this.cameraService.imageAndAnnotation,
        type: 'Wire Breaks',
        other: '',
        date: this.measurementDate,
        level: this.damageLevel,
        observationType: '',
        inspaectionPart: '', linearDamageType: '',
        otherData: {
          damageType: '',
          measurementLength: parseFloat(this.measurementLength),
          layerOptions: this.selectedLayerOption.item,
          locationOptions: this.selectedLocationOption.item,
          supportStatus: this.supportStatus,
          ending: parseFloat(this.measurementEnd),
          observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
          fieldSegment: this.fieldSegment
        }
      };
      if (this.selectedMeasurement) {

        if (this.dataService.isEmpty(object.originalImages) || !this.dataService.isEmpty(object.originalImages)) {
          this.selectedMeasurement.originalImages = object.originalImages;
        }
        object.inspaectionPart = this.selectedMeasurement.inspaectionPart; object.linearDamageType = this.selectedMeasurement.linearDamageType;;
        this.selectedMeasurement.observationType = object.observationType
      }

      if (this.deepEquals(this.selectedMeasurement, object)) {
        this.dataService.navigateToLineTracker(this)
      } else {
        this.utilityService.menuAlert('lineTracker', 'line-tracker-home')
      }
    }
  }
  gotoHome() {
    if (!this.isMeasurementEdit) {
      if (this.measurementStart != undefined || this.measurementStart != '' ||
        this.damageLevel != undefined || this.damageLevel != '' ||
        this.observationNote != undefined || this.observationNote != '' ||
        this.measurementImageList.length != 0 || this.measurementEnd != undefined || this.measurementLength != undefined ||
        this.measurementEnd != '' || this.measurementLength != '' ||
        this.noOfBreaks != undefined || this.noOfBreaks != '') {
        this.utilityService.menuAlert('home', 'home')
      } else {
        this.router.navigate(['home']);
      }
    } else {
      var object = {
        id: this.measurementId,
        end: null,
        external: this.noOfBreaks,
        start: parseFloat(this.measurementStart),
        internal: 0,
        internalImage: '',
        externalImage: this.cameraService.editedImg,
        originalImages: this.cameraService.imageAndAnnotation,
        type: 'Wire Breaks',
        other: '',
        date: this.measurementDate,
        level: this.damageLevel,
        observationType: '',
        inspaectionPart: '', linearDamageType: '',
        otherData: {
          damageType: '',
          measurementLength: parseFloat(this.measurementLength),
          layerOptions: this.selectedLayerOption.item,
          locationOptions: this.selectedLocationOption.item,
          supportStatus: this.supportStatus,
          ending: parseFloat(this.measurementEnd),
          observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
          fieldSegment: this.fieldSegment
        }
      };
      if (this.selectedMeasurement) {

        if (this.dataService.isEmpty(object.originalImages) || !this.dataService.isEmpty(object.originalImages)) {
          this.selectedMeasurement.originalImages = object.originalImages;
        }
        object.inspaectionPart = this.selectedMeasurement.inspaectionPart; object.linearDamageType = this.selectedMeasurement.linearDamageType;;
        this.selectedMeasurement.observationType = object.observationType
      }

      if (this.deepEquals(this.selectedMeasurement, object)) {
        this.router.navigate(['home']);
      } else {
        this.utilityService.menuAlert('home', 'home')
      }
    }
  }
  gotoInspections() {
    if (!this.isMeasurementEdit) {
      if (this.measurementStart != undefined || this.measurementStart != '' ||
        this.damageLevel != undefined || this.damageLevel != '' ||
        this.observationNote != undefined || this.observationNote != '' ||
        this.measurementImageList.length != 0 || this.measurementEnd != undefined || this.measurementLength != undefined ||
        this.measurementEnd != '' || this.measurementLength != '' ||
        this.noOfBreaks != undefined || this.noOfBreaks != '') {
        this.utilityService.menuAlert('inspections', 'inspection-home')
      } else {
        this.router.navigate(['inspection-home']);
      }
    } else {
      var object = {
        id: this.measurementId,
        end: null,
        start: parseFloat(this.measurementStart),
        external: this.noOfBreaks,
        internal: 0,
        internalImage: '',
        externalImage: this.cameraService.editedImg,
        originalImages: this.cameraService.imageAndAnnotation,
        type: 'Wire Breaks',
        other: '',
        date: this.measurementDate,
        level: this.damageLevel,
        observationType: '',
        inspaectionPart: '', linearDamageType: '',
        otherData: {
          damageType: '',
          measurementLength: parseFloat(this.measurementLength),
          layerOptions: this.selectedLayerOption.item,
          locationOptions: this.selectedLocationOption.item,
          supportStatus: this.supportStatus,
          ending: parseFloat(this.measurementEnd),
          observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
          fieldSegment: this.fieldSegment
        }
      };
      if (this.selectedMeasurement) {

        if (this.dataService.isEmpty(object.originalImages) || !this.dataService.isEmpty(object.originalImages)) {
          this.selectedMeasurement.originalImages = object.originalImages;
        }
        object.inspaectionPart = this.selectedMeasurement.inspaectionPart; object.linearDamageType = this.selectedMeasurement.linearDamageType;;
        this.selectedMeasurement.observationType = object.observationType
      }

      if (this.deepEquals(this.selectedMeasurement, object)) {
        this.router.navigate(['inspection-home']);
      } else {
        this.utilityService.menuAlert('inspections', 'inspection-home')
      }
    }
  }
  gotoResources() {
    if(this.device.platform == "browser") {
      this.dataService.gotoResources();
      return;
    }
    if (!this.isMeasurementEdit) {
      if (this.measurementStart != undefined || this.measurementStart != '' ||
        this.damageLevel != undefined || this.damageLevel != '' ||
        this.observationNote != undefined || this.observationNote != '' ||
        this.measurementImageList.length != 0 || this.measurementEnd != undefined || this.measurementLength != undefined ||
        this.measurementEnd != '' || this.measurementLength != '' ||
        this.noOfBreaks != undefined || this.noOfBreaks != '') {
        this.utilityService.menuAlert('resources', 'resource')
      } else {
        this.router.navigate(['resource']);
      }
    } else {
      var object = {
        id: this.measurementId,
        end: null,
        start: parseFloat(this.measurementStart),
        external: this.noOfBreaks,
        internal: 0,
        internalImage: '',
        externalImage: this.cameraService.editedImg,
        originalImages: this.cameraService.imageAndAnnotation,
        type: 'Wire Breaks',
        other: '',
        date: this.measurementDate,
        level: this.damageLevel,
        observationType: '',
        inspaectionPart: '', linearDamageType: '',
        otherData: {
          damageType: '',
          measurementLength: parseFloat(this.measurementLength),
          layerOptions: this.selectedLayerOption.item,
          locationOptions: this.selectedLocationOption.item,
          supportStatus: this.supportStatus,
          ending: parseFloat(this.measurementEnd),
          observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
          fieldSegment: this.fieldSegment
        }
      };
      if (this.selectedMeasurement) {

        if (this.dataService.isEmpty(object.originalImages) || !this.dataService.isEmpty(object.originalImages)) {
          this.selectedMeasurement.originalImages = object.originalImages;
        }
        object.inspaectionPart = this.selectedMeasurement.inspaectionPart; object.linearDamageType = this.selectedMeasurement.linearDamageType;;
        this.selectedMeasurement.observationType = object.observationType
      }

      if (this.deepEquals(this.selectedMeasurement, object)) {
        this.router.navigate(['resource']);
      } else {
        this.utilityService.menuAlert('resources', 'resource')
      }
    }
  }
  gotoContact() {
    if(this.device.platform == "browser") {
      this.dataService.gotoContact();
      return;
    }
    if (!this.isMeasurementEdit) {
      if (this.measurementStart != undefined || this.measurementStart != '' ||
        this.damageLevel != undefined || this.damageLevel != '' ||
        this.observationNote != undefined || this.observationNote != '' ||
        this.measurementImageList.length != 0 || this.measurementEnd != undefined || this.measurementLength != undefined ||
        this.measurementEnd != '' || this.measurementLength != '' ||
        this.noOfBreaks != undefined || this.noOfBreaks != '') {
        this.utilityService.menuAlert('contact', 'contact')
      } else {
        this.router.navigate(['contact']);
      }
    } else {
      var object = {
        id: this.measurementId,
        end: null,
        start: parseFloat(this.measurementStart),
        external: this.noOfBreaks,
        internal: 0,
        internalImage: '',
        externalImage: this.cameraService.editedImg,
        originalImages: this.cameraService.imageAndAnnotation,
        type: 'Wire Breaks',
        other: '',
        date: this.measurementDate,
        level: this.damageLevel,
        observationType: '',
        inspaectionPart: '', linearDamageType: '',
        otherData: {
          damageType: '',
          measurementLength: parseFloat(this.measurementLength),
          layerOptions: this.selectedLayerOption.item,
          locationOptions: this.selectedLocationOption.item,
          supportStatus: this.supportStatus,
          ending: parseFloat(this.measurementEnd),
          observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
          fieldSegment: this.fieldSegment
        }
      };
      if (this.selectedMeasurement) {

        if (this.dataService.isEmpty(object.originalImages) || !this.dataService.isEmpty(object.originalImages)) {
          this.selectedMeasurement.originalImages = object.originalImages;
        }
        object.inspaectionPart = this.selectedMeasurement.inspaectionPart; object.linearDamageType = this.selectedMeasurement.linearDamageType;;
        this.selectedMeasurement.observationType = object.observationType
      }

      if (this.deepEquals(this.selectedMeasurement, object)) {
        this.router.navigate(['contact']);
      } else {
        this.utilityService.menuAlert('contact', 'contact')
      }
    }
  }

  ionViewDidEnter() {
    this.cameraStarted = false;
    this.readFlag = this.utilityService.getObservationReadFlag();
    if (this.readFlag === 'readOnly' && this.isMeasurementEdit === true) {
      this.readOnly = true;
      this.disableFormFields();
    }
    this.filteredvalues = this.utilityService.getAllData().filter(t => t.DATA.type === 'Wire Breaks');
  }
  ionViewDidLeave() {
    this.helpService.exitHelpMode();
    if(this.cameraStarted == false) {
      this.measurementImageList = [];
      this.measurementStart = null;
      this.damageLevel = '';
      this.measurementEnd = null;
    }
  }
  test() {
  }

  hasErrorStart(selectedField) {
    switch (selectedField) {
      case 'start':
        if (parseFloat(this.measurementStart) > this.inspectionHeader.INSPECTED_LENGTH) {
          this.startForm.controls['measurementStartCtrl'].setErrors({ 'incorrect': true })
          this.startErrorMessage = "Start value cannot be greater than inspected length " + this.inspectionHeader.INSPECTED_LENGTH
          return true;
        } else if (parseFloat(this.measurementStart) < 0) {
          this.startForm.controls['measurementStartCtrl'].setErrors({ 'incorrect': true })
          this.startErrorMessage = "Start value cannot be less than 0"
          return true;
        } else if ((this.measurementStart == '' && this.measurementStart != 0) || this.measurementStart == null || this.measurementStart == undefined) {
          this.startForm.controls['measurementStartCtrl'].setErrors({ 'incorrect': true })
          this.startErrorMessage = "Start value is mandatory"
          return true;
        } else if (isNaN(parseFloat(this.measurementStart))) {
          this.startForm.controls['measurementStartCtrl'].setErrors({ 'incorrect': true })
          this.startErrorMessage = "Invalid start value"
          return true;
        } else {
          return false;
        }
        break;
      case 'length':
        if (parseFloat(this.measurementLength) + parseFloat(this.measurementStart) > this.inspectionHeader.INSPECTED_LENGTH) {
          this.lengthForm.controls['measurementLengthCtrl'].setErrors({ 'incorrect': true })
          this.lengthErrorMessage = "Measurement Length should not be greater than inspected length " + this.inspectionHeader.INSPECTED_LENGTH
          return true;
        } else if (parseFloat(this.measurementLength) <= 0) {
          this.lengthForm.controls['measurementLengthCtrl'].setErrors({ 'incorrect': true })
          this.lengthErrorMessage = "Length should be greater than 0"
          return true;
        } else if (this.measurementLength == '' || this.measurementLength == null || this.measurementLength == undefined) {
          this.lengthForm.controls['measurementLengthCtrl'].setErrors({ 'incorrect': true })
          this.lengthErrorMessage = "Length value is mandatory"
          return true;
        } else if (isNaN(parseFloat(this.measurementLength))) {
          this.lengthForm.controls['measurementLengthCtrl'].setErrors({ 'incorrect': true })
          this.lengthErrorMessage = "Invalid length value"
          return true;
        } else {
          var tempUnit = this.inspectionHeader.LENGTH_UOM
          return false;
        }
        break;
      case 'end':
        if (parseFloat(this.measurementEnd) > this.inspectionHeader.INSPECTED_LENGTH) {
          this.endForm.controls['measurementEndCtrl'].setErrors({ 'incorrect': true })
          this.endErrorMessage = "Measurement end should not be greater than inspected length " + this.inspectionHeader.INSPECTED_LENGTH
          return true;
        } else if (parseFloat(this.measurementEnd) <= parseFloat(this.measurementStart)) {
          this.endForm.controls['measurementEndCtrl'].setErrors({ 'incorrect': true })
          this.endErrorMessage = "End should be greater than start " + this.measurementStart
          return true;
        } else if (parseFloat(this.measurementEnd) <= 0) {
          this.endForm.controls['measurementEndCtrl'].setErrors({ 'incorrect': true })
          this.endErrorMessage = "End should be greater than start " + this.measurementStart
          return true;
        } else if (this.measurementEnd == '' || this.measurementEnd == null || this.measurementEnd == undefined) {
          this.endForm.controls['measurementEndCtrl'].setErrors({ 'incorrect': true })
          this.endErrorMessage = "End value is mandatory"
          return true;
        } else if (isNaN(parseFloat(this.measurementEnd))) {
          this.endForm.controls['measurementEndCtrl'].setErrors({ 'incorrect': true })
          this.endErrorMessage = "Invalid end value"
          return true;
        } else return false;
        break;
      case 'noOfBreakes':
        if (parseFloat(this.noOfBreaks) <= 0) {
          this.noOfBreaksForm.controls['noOfBreaksCtrl'].setErrors({ 'incorrect': true })
          this.noOfBreakesErrorMessage = "End should be greater than start 0"
          return true;
        } else if (this.noOfBreaks == '' || this.noOfBreaks == null || this.noOfBreaks == undefined) {
          this.noOfBreaksForm.controls['noOfBreaksCtrl'].setErrors({ 'incorrect': true })
          this.noOfBreakesErrorMessage = "End value is mandatory"
          return true;
        } else if (isNaN(parseFloat(this.noOfBreaks))) {
          this.noOfBreaks.controls.noOfBreaksCtrl.setErrors({ 'incorrect': true })
          this.noOfBreakesErrorMessage = "Invalid end value"
          return true;
        } else return false;
        break;
    }
  }

  keyPressed(event: any, value: any, setEnding?: string) {
    console.log("key pressed")
    if (event.key != "Backspace") {
      if (value && value != null) {
        var tempStart = value.toString() + event.key;
        if (!(/^([0-9]+)?([.]?[0-9]{0,3})?$/.test(tempStart))) {
          console.log("key pressed" + value)
          return false;
        }
      } else {
        if (!(/^([0-9]+)?([.]?[0-9]{0,3})?$/).test(event.key)) {
          console.log("key pressed" + value)
          return false;
        }
      }
    }
  }

  onChangeDisable(selectedField) {
    switch (selectedField) {
      case 'start':
        if (this.measurementStart != '' && this.startForm.controls['measurementStartCtrl'].valid) {
          this.lengthForm.controls['measurementLengthCtrl'].enable()
          this.endForm.controls['measurementEndCtrl'].enable()
          if (this.fieldSegment == 'length') {
            if (this.measurementLength != '' && this.lengthForm.controls['measurementLengthCtrl'].valid) {
              this.measurementEnd = (isNaN(parseFloat(this.measurementStart) + parseFloat(this.measurementLength)) || (parseFloat(this.measurementStart) + parseFloat(this.measurementLength)) < 0) ? '0' : (((parseFloat(this.measurementStart) * 1000) + (parseFloat(this.measurementLength) * 1000)) / 1000).toString();
            }
          } else if (this.fieldSegment == 'end') {
            if (this.measurementEnd != '' && this.endForm.controls['measurementEndCtrl'].valid) {
              this.measurementLength = (isNaN(parseFloat(this.measurementEnd) - parseFloat(this.measurementStart)) || (parseFloat(this.measurementEnd) - parseFloat(this.measurementStart)) < 0) ? '0' : (((parseFloat(this.measurementEnd) * 1000) - (parseFloat(this.measurementStart) * 1000)) / 1000).toString();
            }
          }
          this.isStartValid = true;
        } else {
          this.lengthForm.controls['measurementLengthCtrl'].disable()
          this.endForm.controls['measurementEndCtrl'].disable()
          this.measurementEnd = '';
          this.measurementLength = '';
          this.isStartValid = false;
        }
        break;
      case 'length':
        this.measurementEnd = (isNaN(parseFloat(this.measurementStart) + parseFloat(this.measurementLength)) || (parseFloat(this.measurementStart) + parseFloat(this.measurementLength)) < 0) ? '0' : (((parseFloat(this.measurementStart) * 1000) + (parseFloat(this.measurementLength) * 1000)) / 1000).toString();
        break;
      case 'end':
        this.measurementLength = (isNaN(parseFloat(this.measurementEnd) - parseFloat(this.measurementStart)) || (parseFloat(this.measurementEnd) - parseFloat(this.measurementStart)) < 0) ? '0' : (((parseFloat(this.measurementEnd) * 1000) - (parseFloat(this.measurementStart) * 1000)) / 1000).toString();
        break;
    }
  }

  disableFormFields() {
    if (this.helpService.helpMode == true || this.readOnly == true) {
      this.startForm.controls['measurementStartCtrl'].disable();
      this.endForm.controls['measurementEndCtrl'].disable();
      this.lengthForm.controls['measurementLengthCtrl'].disable()
      this.noOfBreaksForm.controls['noOfBreaksCtrl'].disable();
    } else {
      this.startForm.controls['measurementStartCtrl'].enable();
      if (this.isStartValid == true) {
        this.endForm.controls['measurementEndCtrl'].enable();
        this.lengthForm.controls['measurementLengthCtrl'].enable()
      }
      this.noOfBreaksForm.controls['noOfBreaksCtrl'].enable();
    }
  }

  takeMeasurement(selectedField) {
    measurement.takeMeasurement({ "UOM": this.dataService.selectedUom }, res => {
      // alert("MEASUREMENT VALUE = " + res);
      this.ngZone.run(() => {
        if (res != '') {
          switch (selectedField) {
            case 'start':
              this.measurementStart = res;
              this.endForm.controls['measurementEndCtrl'].enable();
              this.lengthForm.controls['measurementLengthCtrl'].enable();
              this.isStartValid = true;
              break;
            case 'end':
              this.measurementEnd = res;
              this.measurementLength = (isNaN(parseFloat(this.measurementEnd) - parseFloat(this.measurementStart)) || (parseFloat(this.measurementEnd) - parseFloat(this.measurementStart)) < 0) ? '0' : (((parseFloat(this.measurementEnd) * 1000) - (parseFloat(this.measurementStart) * 1000)) / 1000).toString();
              break;
            case 'length':
              this.measurementLength = res;
              this.measurementEnd = (isNaN(parseFloat(this.measurementStart) + parseFloat(this.measurementLength)) || (parseFloat(this.measurementStart) + parseFloat(this.measurementLength)) < 0) ? '0' : (((parseFloat(this.measurementStart) * 1000) + (parseFloat(this.measurementLength) * 1000)) / 1000).toString();
              break;
          }
        }
      });
    }, err => {
      this.unviredSdk.logError("Measurement", "TakeMeasurement", err);
    });
  }

  saveMeasurement() {
    if (this.startForm.controls['measurementStartCtrl'].valid == false && this.measurementStart != 0) {
      this.alertService.showAlert("", this.translate.instant("Please fill all the fields to continue"));
      return
    } else if (this.fieldSegment == 'length' && this.lengthForm.controls['measurementLengthCtrl'].valid == false) {
      this.alertService.showAlert("", this.translate.instant("Please fill all the fields to continue"));
      return
    } else if (this.fieldSegment == 'end' && this.endForm.controls['measurementEndCtrl'].valid == false) {
      this.alertService.showAlert("", this.translate.instant("Please fill all the fields to continue"));
      return
    } else if(this.noOfBreaksForm.controls['noOfBreaksCtrl'].invalid) {
      this.alertService.showAlert("", this.translate.instant("Please enter valid input for number of brokern wires to continue"));
      return
    }
    // if (this.observationNote == undefined || this.observationNote == '') {
    //   this.alertService.showAlert("", this.translate.instant("Please fill all the fields to continue"));
    //   return
    // }
    if (this.cameraService.editedImg.length == 0 && this.continueWithoutImage == false) {
      this.invalidImageAlert();
    } else if (this.isMeasurementEdit) {
      const obj = {
        id: this.measurementId,
        end: null,
        start: parseFloat(this.measurementStart),
        external: this.noOfBreaks,
        internal: 0,
        internalImage: '',
        externalImage: this.cameraService.editedImg,
        originalImages: this.cameraService.imageAndAnnotation,
        type: 'Wire Breaks',
        other: '',
        date: this.measurementDate,
        level: this.damageLevel,
        observationType: '',
        otherData: {
          damageType: '',
          measurementLength: parseFloat(this.measurementLength),
          layerOptions: this.selectedLayerOption.item,
          locationOptions: this.selectedLocationOption.item,

          ending: parseFloat(this.measurementEnd),
          observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
          fieldSegment: this.fieldSegment
        }
      };
      this.validateDataAndSave(obj)
    } else if (!this.isMeasurementEdit) {
      if (parseFloat(this.measurementStart) > parseFloat(this.inspectionHeader.INSPECTED_LENGTH) && parseFloat(this.measurementEnd) > parseFloat(this.inspectionHeader.INSPECTED_LENGTH)) {
        this.utilityService.startEndValidation(this.inspectionHeader.INSPECTED_LENGTH);
      } else if (parseFloat(this.measurementStart) > parseFloat(this.measurementEnd)) {
        this.utilityService.startValidation();
      } else if (parseFloat(this.measurementEnd) > parseFloat(this.inspectionHeader.INSPECTED_LENGTH)) {
        this.utilityService.endValidation(parseFloat(this.inspectionHeader.INSPECTED_LENGTH));;
      } else if (parseFloat(this.measurementStart) === parseFloat(this.measurementEnd)) {
        this.utilityService.totallengthSameValidation();
      } else if (parseFloat(this.measurementStart) <= parseFloat(this.measurementEnd) && ((parseFloat(this.measurementStart) + parseFloat(this.measurementLength)) <= parseFloat(this.inspectionHeader.INSPECTED_LENGTH))) {
        const obj2 = {
          id: this.measurementId,
          start: parseFloat(this.measurementStart),
          external: this.noOfBreaks,
          internal: 0,
          internalImage: '',
          externalImage: this.cameraService.editedImg,
          originalImages: this.cameraService.imageAndAnnotation,
          type: 'Wire Breaks',
          other: '',
          date: this.measurementDate,
          end: null,
          level: this.damageLevel,
          otherData: {
            damageType: '',
            measurementLength: parseFloat(this.measurementLength),
            layerOptions: this.selectedLayerOption.item,
            locationOptions: this.selectedLocationOption.item,
            supportStatus: this.supportStatus,
            ending: parseFloat(this.measurementEnd),
            observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
            fieldSegment: this.fieldSegment
          }
        };
        this.validateDataAndSave(obj2)
      }
    }
  }

  saveOnPause() {
    if (this.isMeasurementEdit) {
      const obj = {
        id: this.measurementId,
        end: null,
        start: parseFloat(this.measurementStart),
        external: this.noOfBreaks,
        internal: 0,
        internalImage: '',
        externalImage: this.cameraService.editedImg,
        originalImages: this.cameraService.imageAndAnnotation,
        type: 'Wire Breaks',
        other: '',
        date: this.measurementDate,
        level: this.damageLevel,
        observationType: '',
        otherData: {
          damageType: '',
          measurementLength: parseFloat(this.measurementLength),
          layerOptions: this.selectedLayerOption.item,
          locationOptions: this.selectedLocationOption.item,

          ending: parseFloat(this.measurementEnd),
          observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
          fieldSegment: this.fieldSegment
        }
      };
      this.dataService.saveMeasurements(obj);
    } else if (!this.isMeasurementEdit) {
      const obj2 = {
        id: this.measurementId,
        start: parseFloat(this.measurementStart),
        external: this.noOfBreaks,
        internal: 0,
        internalImage: '',
        externalImage: this.cameraService.editedImg,
        originalImages: this.cameraService.imageAndAnnotation,
        type: 'Wire Breaks',
        other: '',
        date: this.measurementDate,
        end: null,
        level: this.damageLevel,
        otherData: {
          damageType: '',
          measurementLength: parseFloat(this.measurementLength),
          layerOptions: this.selectedLayerOption.item,
          locationOptions: this.selectedLocationOption.item,
          supportStatus: this.supportStatus,
          ending: parseFloat(this.measurementEnd),
          observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
          fieldSegment: this.fieldSegment
        }
      };
      this.dataService.saveMeasurements(obj2);

    }
  }



  async invalidImageAlert() {
    var confAlert = await this.alertController.create({
      backdropDismiss: false,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      message: '<strong>' + this.translate.instant('No photo associated with observation, are you sure you want to continue?') + '</strong>',
      buttons: [
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
            this.continueWithoutImage = false;
          }
        }, {
          text: this.translate.instant('Continue'),
          role: 'continue',
          handler: () => {
            this.continueWithoutImage = true;
            this.saveMeasurement();
          }
        }
      ]
    });
    await confAlert.present();
  }

  validateDataAndSave(object) {
    if (this.selectedMeasurement) {
      if (this.dataService.isEmpty(object.originalImages) || !this.dataService.isEmpty(object.originalImages)) {
        this.selectedMeasurement.originalImages = object.originalImages;
      }
      object.inspaectionPart = this.selectedMeasurement.inspaectionPart; object.linearDamageType = this.selectedMeasurement.linearDamageType;;
      this.selectedMeasurement.observationType = object.observationType
      object.level = this.selectedMeasurement.level;
      object.end = this.selectedMeasurement.end;
    }

    if (this.deepEquals(this.selectedMeasurement, object)) {
      this.alertService.showAlert("", this.translate.instant("No changes to save."))
    } else {
      if (isNaN(object.start) || isNaN(object.otherData.measurementLength) || isNaN(object.otherData.ending)) {
        this.alertService.showAlert("", this.translate.instant("Please enter valid values to save"))
        return;
      }
      if (parseFloat(object.start) > this.inspectionHeader.INSPECTED_LENGTH || object.otherData.ending > this.inspectionHeader.INSPECTED_LENGTH) {
        this.alertService.showAlert("", this.translate.instant("Start and end should be less than inspection length") + " " + this.inspectionHeader.INSPECTED_LENGTH)
        return;
      }
      if (parseFloat(object.start) == parseFloat(object.otherData.ending)) {
        this.alertService.showAlert("", this.translate.instant("Inspection length cannot be 0"))
        return;
      }
      if (object.externalImage.length == 0 && this.continueWithoutImage == false) {
        this.invalidImageAlert()
        return;
      }
      this.dataService.saveMeasurements(object);
      this.router.navigate(['observations']);
      this.isMeasurementEdit = false;
      this.cameraService.reset();
    }

  }

  deepEquals(selectedMeasurement, currentMeasurement) {
    if (this.dirty == true || this.cameraService.measurementEdited == true) {
      return false;
    }
    if (selectedMeasurement === currentMeasurement) {
      return true; // if both x and currentMeasurement are null or undefined and exactly the same
    } else if (!(selectedMeasurement instanceof Object) || !(currentMeasurement instanceof Object)) {
      return false; // if they are not strictly equal, they both need to be Objects
    } else if (selectedMeasurement.constructor !== currentMeasurement.constructor) {
      // they must have the exact same prototype chain, the closest we can do is
      // test their constructor.
      return false;
    } else {
      for (const p in selectedMeasurement) {
        if (!selectedMeasurement.hasOwnProperty(p)) {
          continue; // other properties were tested using x.constructor === y.constructor
        }
        if (!currentMeasurement.hasOwnProperty(p)) {
          return false; // allows to compare x[ p ] and currentMeasurement[ p ] when set to undefined
        }
        if (selectedMeasurement[p] === currentMeasurement[p]) {
          continue; // if they have the same strict value or identity then they are equal
        }
        if (typeof (selectedMeasurement[p]) !== 'object') {
          return false; // Numbers, Strings, Functions, Booleans must be strictly equal
        }
        if (!this.deepEquals(selectedMeasurement[p], currentMeasurement[p])) {
          return false;
        }
      }
      for (const p in currentMeasurement) {
        if (currentMeasurement.hasOwnProperty(p) && !selectedMeasurement.hasOwnProperty(p)) {
          return false;
        }
      }
      return true;
    }
  }

  async deleteImage(index) {
    const alert = await this.alertController.create({
      backdropDismiss: false,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      header: this.translate.instant('Are you sure?'),
      message: '<strong>' + this.translate.instant('You want to delete this image') + '</strong>!',
      buttons: [
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
          }
        }, {
          text: this.translate.instant('Okay'),
          handler: () => {
            this.setChanged();
            // this.measurementImageList = this.cameraService.editedImg
            this.measurementImageList.splice(index, 1);
            if (this.measurementImageList.length == 0) {
              this.measurementImageList = false;
              this.measurementImageList = [];
            }
            if (this.cameraService.imageAndAnnotation.originalImgList != undefined) {
              this.cameraService.imageAndAnnotation.originalImgList.splice(index, 1)
            }
            this.cameraService.setData(this.measurementImageList);
          }
        }
      ]
    });
    await alert.present();
  }

  setChanged() {
    this.dirty = true;
  }

  backButtonClicked() {
    if (!this.isMeasurementEdit) {
      if (this.measurementStart !== undefined || this.damageLevel != undefined || this.measurementImageList.length != 0 || this.measurementEnd != undefined || this.selectedLayerOption.item != this.layerOptionsList[0].item || this.selectedLocationOption.item != this.legOptinsList[0].item ||
        this.noOfBreaks != undefined) {
        this.router.navigate(['wire-breaks']);
        this.utilityService.backAlert();
      } else {
        this.router.navigate(['new-observation']);
      }
    } else {
      var object = {
        id: this.measurementId,
        end: null,
        start: parseFloat(this.measurementStart),
        external: this.noOfBreaks,
        internal: 0,
        internalImage: '',
        externalImage: this.cameraService.editedImg,
        originalImages: this.cameraService.imageAndAnnotation,
        type: 'Wire Breaks',
        other: '',
        date: this.measurementDate,
        level: this.damageLevel,
        observationType: '',
        inspaectionPart: '', linearDamageType: '',
        otherData: {
          damageType: '',
          measurementLength: parseFloat(this.measurementLength),
          layerOptions: this.selectedLayerOption.item,
          locationOptions: this.selectedLocationOption.item,
          supportStatus: this.supportStatus,
          ending: parseFloat(this.measurementEnd),
          observationNotes: (this.observationNote != undefined && this.observationNote != '') ? this.observationNote : '',
          fieldSegment: this.fieldSegment
        }
      };
      if (this.selectedLocationOption.item) {

        if (this.dataService.isEmpty(object.originalImages) || !this.dataService.isEmpty(object.originalImages)) {
          this.selectedMeasurement.originalImages = object.originalImages;
        }
        object.inspaectionPart = this.selectedMeasurement.inspaectionPart; object.linearDamageType = this.selectedMeasurement.linearDamageType;
        this.selectedMeasurement.observationType = object.observationType
      }

      if (this.deepEquals(this.selectedMeasurement, object)) {
        this.router.navigate(['observations']);
      } else {
        this.router.navigate(['wire-breaks']);
        this.utilityService.backAlert(true);
      }
    }

  }

  wireChanged(event:any) {
    if (event.target.value.toString().length > 1) {
      if (event.key!='Backspace')  {
        this.noOfBreaksForm.controls['noOfBreaksCtrl'].markAsTouched();
        this.noOfBreaksForm.controls['noOfBreaksCtrl'].setErrors({ maxlength: true });
        event.preventDefault();
        return false;
      }
    }
  }

  reset() {
    this.noOfBreaksForm.controls['noOfBreaksCtrl'].clearValidators();
    this.noOfBreaksForm.controls['noOfBreaksCtrl'].updateValueAndValidity();
  }
}